[{"C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\dashboard\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\mobile\\mobile-form.tsx": "2", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\mobile\\mobile-nav.tsx": "3", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\mobile\\mobile-table.tsx": "4", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\providers.tsx": "5", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\pwa\\pwa-provider.tsx": "6", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\ui\\button.tsx": "7", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\ui\\card.tsx": "8", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\ui\\dialog.tsx": "9", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\ui\\input.tsx": "10", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\ui\\label.tsx": "11", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\ui\\select.tsx": "12", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\ui\\table.tsx": "13", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\lib\\auth\\config.ts": "14", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\lib\\database\\prisma.ts": "15", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\lib\\utils\\cn.ts": "16", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\analytics\\route.ts": "17", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\announcements\\route.ts": "18", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\announcements\\[id]\\route.ts": "19", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\assessments\\route.ts": "20", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\assessments\\[id]\\results\\route.ts": "21", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\assessments\\[id]\\route.ts": "22", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\attendance\\analytics\\route.ts": "23", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\attendance\\route.ts": "24", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\attendance\\[id]\\route.ts": "25", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "26", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\courses\\route.ts": "27", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\export\\route.ts": "28", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\groups\\route.ts": "29", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\inter-server\\assessments\\route.ts": "30", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\inter-server\\assessments\\[id]\\route.ts": "31", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\inter-server\\messages\\broadcast\\route.ts": "32", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\leads\\route.ts": "33", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\leads\\[id]\\route.ts": "34", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\messages\\route.ts": "35", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\messages\\[id]\\route.ts": "36", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\payments\\route.ts": "37", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\payments\\[id]\\route.ts": "38", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\search\\global\\route.ts": "39", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\students\\route.ts": "40", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\students\\[id]\\route.ts": "41", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\sync\\announcements\\route.ts": "42", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\sync\\students\\route.ts": "43", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\users\\route.ts": "44", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\users\\[id]\\route.ts": "45", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\auth\\error\\page.tsx": "46", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\auth\\layout.tsx": "47", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\auth\\signin\\page.tsx": "48", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\analytics\\page.tsx": "49", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\assessments\\page.tsx": "50", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\attendance\\page.tsx": "51", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\groups\\page.tsx": "52", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\layout.tsx": "53", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\leads\\page.tsx": "54", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\messages\\page.tsx": "55", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\page.tsx": "56", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\payments\\page.tsx": "57", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\staff\\page.tsx": "58", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\students\\page.tsx": "59", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\layout.tsx": "60", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\offline\\page.tsx": "61", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\page.tsx": "62", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\components\\filters\\advanced-filters.tsx": "63", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\components\\search\\global-search.tsx": "64", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\hooks\\use-debounce.ts": "65"}, {"size": 5999, "mtime": 1750396617758, "results": "66", "hashOfConfig": "67"}, {"size": 3743, "mtime": 1750399650138, "results": "68", "hashOfConfig": "67"}, {"size": 4225, "mtime": 1750399579736, "results": "69", "hashOfConfig": "67"}, {"size": 3404, "mtime": 1750399896562, "results": "70", "hashOfConfig": "67"}, {"size": 309, "mtime": 1750399343494, "results": "71", "hashOfConfig": "67"}, {"size": 5820, "mtime": 1750399906540, "results": "72", "hashOfConfig": "67"}, {"size": 1837, "mtime": 1750363544323, "results": "73", "hashOfConfig": "67"}, {"size": 1879, "mtime": 1750363563434, "results": "74", "hashOfConfig": "67"}, {"size": 3858, "mtime": 1750363605012, "results": "75", "hashOfConfig": "67"}, {"size": 826, "mtime": 1750363551651, "results": "76", "hashOfConfig": "67"}, {"size": 712, "mtime": 1750363571715, "results": "77", "hashOfConfig": "67"}, {"size": 5617, "mtime": 1750363668560, "results": "78", "hashOfConfig": "67"}, {"size": 2767, "mtime": 1750363587592, "results": "79", "hashOfConfig": "67"}, {"size": 1870, "mtime": 1750363280474, "results": "80", "hashOfConfig": "67"}, {"size": 279, "mtime": 1750363287162, "results": "81", "hashOfConfig": "67"}, {"size": 166, "mtime": 1750363533497, "results": "82", "hashOfConfig": "67"}, {"size": 7194, "mtime": 1750393938447, "results": "83", "hashOfConfig": "67"}, {"size": 3913, "mtime": 1750395882507, "results": "84", "hashOfConfig": "67"}, {"size": 4612, "mtime": 1750395901412, "results": "85", "hashOfConfig": "67"}, {"size": 6278, "mtime": 1750395789891, "results": "86", "hashOfConfig": "67"}, {"size": 3773, "mtime": 1750395329115, "results": "87", "hashOfConfig": "67"}, {"size": 4141, "mtime": 1750395311667, "results": "88", "hashOfConfig": "67"}, {"size": 7110, "mtime": 1750394280383, "results": "89", "hashOfConfig": "67"}, {"size": 7645, "mtime": 1750394223871, "results": "90", "hashOfConfig": "67"}, {"size": 6436, "mtime": 1750394247890, "results": "91", "hashOfConfig": "67"}, {"size": 164, "mtime": 1750400246518, "results": "92", "hashOfConfig": "67"}, {"size": 3783, "mtime": 1750365450533, "results": "93", "hashOfConfig": "67"}, {"size": 10240, "mtime": 1750396506143, "results": "94", "hashOfConfig": "67"}, {"size": 4660, "mtime": 1750365492782, "results": "95", "hashOfConfig": "67"}, {"size": 4478, "mtime": 1750395700836, "results": "96", "hashOfConfig": "67"}, {"size": 3962, "mtime": 1750395717956, "results": "97", "hashOfConfig": "67"}, {"size": 2984, "mtime": 1750396353548, "results": "98", "hashOfConfig": "67"}, {"size": 4737, "mtime": 1750365599744, "results": "99", "hashOfConfig": "67"}, {"size": 7030, "mtime": 1750365629299, "results": "100", "hashOfConfig": "67"}, {"size": 5640, "mtime": 1750396385134, "results": "101", "hashOfConfig": "67"}, {"size": 7126, "mtime": 1750396400085, "results": "102", "hashOfConfig": "67"}, {"size": 5510, "mtime": 1750393719538, "results": "103", "hashOfConfig": "67"}, {"size": 4971, "mtime": 1750393741140, "results": "104", "hashOfConfig": "67"}, {"size": 10742, "mtime": 1750396463813, "results": "105", "hashOfConfig": "67"}, {"size": 3957, "mtime": 1750365348909, "results": "106", "hashOfConfig": "67"}, {"size": 4701, "mtime": 1750365369911, "results": "107", "hashOfConfig": "67"}, {"size": 5258, "mtime": 1750394081260, "results": "108", "hashOfConfig": "67"}, {"size": 5981, "mtime": 1750394057991, "results": "109", "hashOfConfig": "67"}, {"size": 3740, "mtime": 1750364571570, "results": "110", "hashOfConfig": "67"}, {"size": 4639, "mtime": 1750364592817, "results": "111", "hashOfConfig": "67"}, {"size": 1641, "mtime": 1750400138107, "results": "112", "hashOfConfig": "67"}, {"size": 303, "mtime": 1750400099156, "results": "113", "hashOfConfig": "67"}, {"size": 3592, "mtime": 1750400123145, "results": "114", "hashOfConfig": "67"}, {"size": 14208, "mtime": 1750394020958, "results": "115", "hashOfConfig": "67"}, {"size": 21319, "mtime": 1750395486930, "results": "116", "hashOfConfig": "67"}, {"size": 21434, "mtime": 1750394345291, "results": "117", "hashOfConfig": "67"}, {"size": 19740, "mtime": 1750365555166, "results": "118", "hashOfConfig": "67"}, {"size": 243, "mtime": 1750400230979, "results": "119", "hashOfConfig": "67"}, {"size": 21672, "mtime": 1750365697825, "results": "120", "hashOfConfig": "67"}, {"size": 31091, "mtime": 1750396228167, "results": "121", "hashOfConfig": "67"}, {"size": 5749, "mtime": 1750400215168, "results": "122", "hashOfConfig": "67"}, {"size": 23166, "mtime": 1750393810341, "results": "123", "hashOfConfig": "67"}, {"size": 14698, "mtime": 1750364639416, "results": "124", "hashOfConfig": "67"}, {"size": 18801, "mtime": 1750396682975, "results": "125", "hashOfConfig": "67"}, {"size": 1476, "mtime": 1750397627600, "results": "126", "hashOfConfig": "67"}, {"size": 2719, "mtime": 1750399931762, "results": "127", "hashOfConfig": "67"}, {"size": 779, "mtime": 1750363975558, "results": "128", "hashOfConfig": "67"}, {"size": 10052, "mtime": 1750396583924, "results": "129", "hashOfConfig": "67"}, {"size": 7677, "mtime": 1750396539377, "results": "130", "hashOfConfig": "67"}, {"size": 378, "mtime": 1750396547333, "results": "131", "hashOfConfig": "67"}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "8ci4t3", {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 16, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 14, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\mobile\\mobile-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\mobile\\mobile-nav.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\mobile\\mobile-table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\providers.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\pwa\\pwa-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\ui\\input.tsx", ["327"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\lib\\auth\\config.ts", ["328"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\lib\\database\\prisma.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\lib\\utils\\cn.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\analytics\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\announcements\\route.ts", ["329", "330", "331"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\announcements\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\assessments\\route.ts", ["332", "333", "334"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\assessments\\[id]\\results\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\assessments\\[id]\\route.ts", ["335", "336", "337", "338"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\attendance\\analytics\\route.ts", ["339", "340", "341", "342", "343", "344", "345", "346", "347", "348"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\attendance\\route.ts", ["349", "350", "351", "352"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\attendance\\[id]\\route.ts", ["353", "354", "355"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\courses\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\export\\route.ts", ["356", "357", "358", "359", "360", "361", "362", "363", "364", "365", "366", "367", "368", "369", "370", "371"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\groups\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\inter-server\\assessments\\route.ts", ["372"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\inter-server\\assessments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\inter-server\\messages\\broadcast\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\leads\\route.ts", ["373"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\leads\\[id]\\route.ts", ["374"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\messages\\route.ts", ["375", "376", "377"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\messages\\[id]\\route.ts", ["378"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\payments\\route.ts", ["379"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\payments\\[id]\\route.ts", ["380", "381"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\search\\global\\route.ts", ["382", "383"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\students\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\students\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\sync\\announcements\\route.ts", ["384", "385", "386", "387", "388", "389"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\sync\\students\\route.ts", ["390", "391", "392", "393"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\users\\route.ts", ["394", "395"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\users\\[id]\\route.ts", ["396", "397", "398"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\auth\\error\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\auth\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\auth\\signin\\page.tsx", ["399"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\analytics\\page.tsx", ["400", "401", "402", "403", "404", "405"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\assessments\\page.tsx", ["406", "407", "408", "409", "410", "411", "412", "413"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\attendance\\page.tsx", ["414", "415", "416", "417"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\groups\\page.tsx", ["418", "419", "420", "421", "422", "423"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\leads\\page.tsx", ["424", "425", "426", "427", "428", "429"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\messages\\page.tsx", ["430", "431", "432", "433", "434", "435", "436", "437"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\payments\\page.tsx", ["438", "439", "440", "441", "442"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\staff\\page.tsx", ["443", "444", "445", "446", "447"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\students\\page.tsx", ["448", "449", "450", "451"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\offline\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\components\\filters\\advanced-filters.tsx", ["452", "453", "454", "455", "456", "457", "458", "459", "460", "461", "462", "463", "464", "465"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\components\\search\\global-search.tsx", ["466", "467", "468", "469", "470"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\hooks\\use-debounce.ts", [], [], {"ruleId": "471", "severity": 2, "message": "472", "line": 4, "column": 18, "nodeType": "473", "messageId": "474", "endLine": 4, "endColumn": 28, "suggestions": "475"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 69, "column": 63, "nodeType": "478", "messageId": "479", "endLine": 69, "endColumn": 66, "suggestions": "480"}, {"ruleId": "481", "severity": 2, "message": "482", "line": 15, "column": 7, "nodeType": null, "messageId": "483", "endLine": 15, "endColumn": 31}, {"ruleId": "484", "severity": 2, "message": "485", "line": 39, "column": 9, "nodeType": "473", "messageId": "486", "endLine": 39, "endColumn": 19, "fix": "487"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 39, "column": 16, "nodeType": "478", "messageId": "479", "endLine": 39, "endColumn": 19, "suggestions": "488"}, {"ruleId": "481", "severity": 2, "message": "489", "line": 41, "column": 11, "nodeType": null, "messageId": "483", "endLine": 41, "endColumn": 15}, {"ruleId": "484", "severity": 2, "message": "485", "line": 43, "column": 9, "nodeType": "473", "messageId": "486", "endLine": 43, "endColumn": 19, "fix": "490"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 43, "column": 16, "nodeType": "478", "messageId": "479", "endLine": 43, "endColumn": 19, "suggestions": "491"}, {"ruleId": "481", "severity": 2, "message": "492", "line": 4, "column": 10, "nodeType": null, "messageId": "483", "endLine": 4, "endColumn": 16}, {"ruleId": "481", "severity": 2, "message": "493", "line": 22, "column": 7, "nodeType": null, "messageId": "483", "endLine": 22, "endColumn": 26}, {"ruleId": "481", "severity": 2, "message": "494", "line": 109, "column": 3, "nodeType": null, "messageId": "483", "endLine": 109, "endColumn": 10}, {"ruleId": "481", "severity": 2, "message": "495", "line": 110, "column": 5, "nodeType": null, "messageId": "483", "endLine": 110, "endColumn": 11}, {"ruleId": "484", "severity": 2, "message": "496", "line": 24, "column": 9, "nodeType": "473", "messageId": "486", "endLine": 24, "endColumn": 25, "fix": "497"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 24, "column": 22, "nodeType": "478", "messageId": "479", "endLine": 24, "endColumn": 25, "suggestions": "498"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 99, "column": 29, "nodeType": "478", "messageId": "479", "endLine": 99, "endColumn": 32, "suggestions": "499"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 102, "column": 51, "nodeType": "478", "messageId": "479", "endLine": 102, "endColumn": 54, "suggestions": "500"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 154, "column": 17, "nodeType": "478", "messageId": "479", "endLine": 154, "endColumn": 20, "suggestions": "501"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 154, "column": 25, "nodeType": "478", "messageId": "479", "endLine": 154, "endColumn": 28, "suggestions": "502"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 183, "column": 24, "nodeType": "478", "messageId": "479", "endLine": 183, "endColumn": 27, "suggestions": "503"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 188, "column": 27, "nodeType": "478", "messageId": "479", "endLine": 188, "endColumn": 30, "suggestions": "504"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 189, "column": 19, "nodeType": "478", "messageId": "479", "endLine": 189, "endColumn": 22, "suggestions": "505"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 189, "column": 27, "nodeType": "478", "messageId": "479", "endLine": 189, "endColumn": 30, "suggestions": "506"}, {"ruleId": "481", "severity": 2, "message": "507", "line": 20, "column": 7, "nodeType": null, "messageId": "483", "endLine": 20, "endColumn": 29}, {"ruleId": "484", "severity": 2, "message": "496", "line": 50, "column": 9, "nodeType": "473", "messageId": "486", "endLine": 50, "endColumn": 25, "fix": "508"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 50, "column": 22, "nodeType": "478", "messageId": "479", "endLine": 50, "endColumn": 25, "suggestions": "509"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 256, "column": 40, "nodeType": "478", "messageId": "479", "endLine": 256, "endColumn": 43, "suggestions": "510"}, {"ruleId": "481", "severity": 2, "message": "511", "line": 109, "column": 11, "nodeType": null, "messageId": "483", "endLine": 109, "endColumn": 24}, {"ruleId": "476", "severity": 2, "message": "477", "line": 162, "column": 40, "nodeType": "478", "messageId": "479", "endLine": 162, "endColumn": 43, "suggestions": "512"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 217, "column": 40, "nodeType": "478", "messageId": "479", "endLine": 217, "endColumn": 43, "suggestions": "513"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 29, "column": 15, "nodeType": "478", "messageId": "479", "endLine": 29, "endColumn": 18, "suggestions": "514"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 108, "column": 56, "nodeType": "478", "messageId": "479", "endLine": 108, "endColumn": 59, "suggestions": "515"}, {"ruleId": "481", "severity": 2, "message": "516", "line": 108, "column": 62, "nodeType": null, "messageId": "483", "endLine": 108, "endColumn": 68}, {"ruleId": "476", "severity": 2, "message": "477", "line": 149, "column": 53, "nodeType": "478", "messageId": "479", "endLine": 149, "endColumn": 56, "suggestions": "517"}, {"ruleId": "481", "severity": 2, "message": "516", "line": 149, "column": 59, "nodeType": null, "messageId": "483", "endLine": 149, "endColumn": 65}, {"ruleId": "476", "severity": 2, "message": "477", "line": 172, "column": 53, "nodeType": "478", "messageId": "479", "endLine": 172, "endColumn": 56, "suggestions": "518"}, {"ruleId": "481", "severity": 2, "message": "516", "line": 172, "column": 59, "nodeType": null, "messageId": "483", "endLine": 172, "endColumn": 65}, {"ruleId": "476", "severity": 2, "message": "477", "line": 193, "column": 54, "nodeType": "478", "messageId": "479", "endLine": 193, "endColumn": 57, "suggestions": "519"}, {"ruleId": "481", "severity": 2, "message": "516", "line": 193, "column": 60, "nodeType": null, "messageId": "483", "endLine": 193, "endColumn": 66}, {"ruleId": "476", "severity": 2, "message": "477", "line": 228, "column": 55, "nodeType": "478", "messageId": "479", "endLine": 228, "endColumn": 58, "suggestions": "520"}, {"ruleId": "481", "severity": 2, "message": "516", "line": 228, "column": 61, "nodeType": null, "messageId": "483", "endLine": 228, "endColumn": 67}, {"ruleId": "476", "severity": 2, "message": "477", "line": 257, "column": 56, "nodeType": "478", "messageId": "479", "endLine": 257, "endColumn": 59, "suggestions": "521"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 290, "column": 58, "nodeType": "478", "messageId": "479", "endLine": 290, "endColumn": 61, "suggestions": "522"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 329, "column": 52, "nodeType": "478", "messageId": "479", "endLine": 329, "endColumn": 55, "suggestions": "523"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 332, "column": 16, "nodeType": "478", "messageId": "479", "endLine": 332, "endColumn": 19, "suggestions": "524"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 343, "column": 29, "nodeType": "478", "messageId": "479", "endLine": 343, "endColumn": 32, "suggestions": "525"}, {"ruleId": "481", "severity": 2, "message": "526", "line": 28, "column": 7, "nodeType": null, "messageId": "483", "endLine": 28, "endColumn": 28}, {"ruleId": "481", "severity": 2, "message": "527", "line": 17, "column": 7, "nodeType": null, "messageId": "483", "endLine": 17, "endColumn": 23}, {"ruleId": "481", "severity": 2, "message": "528", "line": 21, "column": 7, "nodeType": null, "messageId": "483", "endLine": 21, "endColumn": 23}, {"ruleId": "481", "severity": 2, "message": "529", "line": 16, "column": 7, "nodeType": null, "messageId": "483", "endLine": 16, "endColumn": 26}, {"ruleId": "484", "severity": 2, "message": "485", "line": 41, "column": 9, "nodeType": "473", "messageId": "486", "endLine": 41, "endColumn": 19, "fix": "530"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 41, "column": 16, "nodeType": "478", "messageId": "479", "endLine": 41, "endColumn": 19, "suggestions": "531"}, {"ruleId": "481", "severity": 2, "message": "532", "line": 16, "column": 7, "nodeType": null, "messageId": "483", "endLine": 16, "endColumn": 24}, {"ruleId": "476", "severity": 2, "message": "477", "line": 169, "column": 40, "nodeType": "478", "messageId": "479", "endLine": 169, "endColumn": 43, "suggestions": "533"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 111, "column": 40, "nodeType": "478", "messageId": "479", "endLine": 111, "endColumn": 43, "suggestions": "534"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 165, "column": 40, "nodeType": "478", "messageId": "479", "endLine": 165, "endColumn": 43, "suggestions": "535"}, {"ruleId": "481", "severity": 2, "message": "536", "line": 7, "column": 7, "nodeType": null, "messageId": "483", "endLine": 7, "endColumn": 25}, {"ruleId": "476", "severity": 2, "message": "477", "line": 21, "column": 29, "nodeType": "478", "messageId": "479", "endLine": 21, "endColumn": 32, "suggestions": "537"}, {"ruleId": "481", "severity": 2, "message": "538", "line": 2, "column": 10, "nodeType": null, "messageId": "483", "endLine": 2, "endColumn": 26}, {"ruleId": "481", "severity": 2, "message": "539", "line": 3, "column": 10, "nodeType": null, "messageId": "483", "endLine": 3, "endColumn": 21}, {"ruleId": "481", "severity": 2, "message": "540", "line": 7, "column": 7, "nodeType": null, "messageId": "483", "endLine": 7, "endColumn": 24}, {"ruleId": "484", "severity": 2, "message": "496", "line": 39, "column": 9, "nodeType": "473", "messageId": "486", "endLine": 39, "endColumn": 25, "fix": "541"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 39, "column": 22, "nodeType": "478", "messageId": "479", "endLine": 39, "endColumn": 25, "suggestions": "542"}, {"ruleId": "481", "severity": 2, "message": "543", "line": 126, "column": 13, "nodeType": null, "messageId": "483", "endLine": 126, "endColumn": 19}, {"ruleId": "481", "severity": 2, "message": "538", "line": 2, "column": 10, "nodeType": null, "messageId": "483", "endLine": 2, "endColumn": 26}, {"ruleId": "481", "severity": 2, "message": "539", "line": 3, "column": 10, "nodeType": null, "messageId": "483", "endLine": 3, "endColumn": 21}, {"ruleId": "484", "severity": 2, "message": "496", "line": 29, "column": 9, "nodeType": "473", "messageId": "486", "endLine": 29, "endColumn": 25, "fix": "544"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 29, "column": 22, "nodeType": "478", "messageId": "479", "endLine": 29, "endColumn": 25, "suggestions": "545"}, {"ruleId": "481", "severity": 2, "message": "546", "line": 59, "column": 36, "nodeType": null, "messageId": "483", "endLine": 59, "endColumn": 44}, {"ruleId": "481", "severity": 2, "message": "547", "line": 116, "column": 13, "nodeType": null, "messageId": "483", "endLine": 116, "endColumn": 21}, {"ruleId": "481", "severity": 2, "message": "547", "line": 39, "column": 13, "nodeType": null, "messageId": "483", "endLine": 39, "endColumn": 21}, {"ruleId": "476", "severity": 2, "message": "477", "line": 93, "column": 23, "nodeType": "478", "messageId": "479", "endLine": 93, "endColumn": 26, "suggestions": "548"}, {"ruleId": "481", "severity": 2, "message": "547", "line": 110, "column": 13, "nodeType": null, "messageId": "483", "endLine": 110, "endColumn": 21}, {"ruleId": "481", "severity": 2, "message": "549", "line": 56, "column": 14, "nodeType": null, "messageId": "483", "endLine": 56, "endColumn": 19}, {"ruleId": "481", "severity": 2, "message": "550", "line": 15, "column": 3, "nodeType": null, "messageId": "483", "endLine": 15, "endColumn": 11}, {"ruleId": "481", "severity": 2, "message": "551", "line": 33, "column": 3, "nodeType": null, "messageId": "483", "endLine": 33, "endColumn": 12}, {"ruleId": "481", "severity": 2, "message": "552", "line": 34, "column": 3, "nodeType": null, "messageId": "483", "endLine": 34, "endColumn": 7}, {"ruleId": "476", "severity": 2, "message": "477", "line": 75, "column": 14, "nodeType": "478", "messageId": "479", "endLine": 75, "endColumn": 17, "suggestions": "553"}, {"ruleId": "554", "severity": 1, "message": "555", "line": 117, "column": 6, "nodeType": "556", "endLine": 117, "endColumn": 22, "suggestions": "557"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 126, "column": 47, "nodeType": "560", "messageId": "561", "suggestions": "562"}, {"ruleId": "481", "severity": 2, "message": "563", "line": 13, "column": 10, "nodeType": null, "messageId": "483", "endLine": 13, "endColumn": 18}, {"ruleId": "481", "severity": 2, "message": "564", "line": 18, "column": 3, "nodeType": null, "messageId": "483", "endLine": 18, "endColumn": 11}, {"ruleId": "481", "severity": 2, "message": "565", "line": 19, "column": 3, "nodeType": null, "messageId": "483", "endLine": 19, "endColumn": 8}, {"ruleId": "481", "severity": 2, "message": "566", "line": 20, "column": 3, "nodeType": null, "messageId": "483", "endLine": 20, "endColumn": 14}, {"ruleId": "481", "severity": 2, "message": "567", "line": 21, "column": 3, "nodeType": null, "messageId": "483", "endLine": 21, "endColumn": 10}, {"ruleId": "481", "severity": 2, "message": "568", "line": 131, "column": 5, "nodeType": null, "messageId": "483", "endLine": 131, "endColumn": 10}, {"ruleId": "476", "severity": 2, "message": "477", "line": 321, "column": 83, "nodeType": "478", "messageId": "479", "endLine": 321, "endColumn": 86, "suggestions": "569"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 340, "column": 84, "nodeType": "478", "messageId": "479", "endLine": 340, "endColumn": 87, "suggestions": "570"}, {"ruleId": "481", "severity": 2, "message": "571", "line": 18, "column": 3, "nodeType": null, "messageId": "483", "endLine": 18, "endColumn": 8}, {"ruleId": "481", "severity": 2, "message": "572", "line": 97, "column": 7, "nodeType": null, "messageId": "483", "endLine": 97, "endColumn": 19}, {"ruleId": "481", "severity": 2, "message": "573", "line": 104, "column": 7, "nodeType": null, "messageId": "483", "endLine": 104, "endColumn": 18}, {"ruleId": "554", "severity": 1, "message": "574", "line": 201, "column": 6, "nodeType": "556", "endLine": 201, "endColumn": 66, "suggestions": "575"}, {"ruleId": "481", "severity": 2, "message": "565", "line": 12, "column": 60, "nodeType": null, "messageId": "483", "endLine": 12, "endColumn": 65}, {"ruleId": "476", "severity": 2, "message": "477", "line": 36, "column": 13, "nodeType": "478", "messageId": "479", "endLine": 36, "endColumn": 16, "suggestions": "576"}, {"ruleId": "481", "severity": 2, "message": "568", "line": 92, "column": 5, "nodeType": null, "messageId": "483", "endLine": 92, "endColumn": 10}, {"ruleId": "476", "severity": 2, "message": "477", "line": 138, "column": 55, "nodeType": "478", "messageId": "479", "endLine": 138, "endColumn": 58, "suggestions": "577"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 139, "column": 45, "nodeType": "478", "messageId": "479", "endLine": 139, "endColumn": 48, "suggestions": "578"}, {"ruleId": "554", "severity": 1, "message": "579", "line": 152, "column": 6, "nodeType": "556", "endLine": 152, "endColumn": 49, "suggestions": "580"}, {"ruleId": "481", "severity": 2, "message": "581", "line": 22, "column": 3, "nodeType": null, "messageId": "483", "endLine": 22, "endColumn": 7}, {"ruleId": "481", "severity": 2, "message": "550", "line": 23, "column": 3, "nodeType": null, "messageId": "483", "endLine": 23, "endColumn": 11}, {"ruleId": "481", "severity": 2, "message": "582", "line": 24, "column": 3, "nodeType": null, "messageId": "483", "endLine": 24, "endColumn": 16}, {"ruleId": "481", "severity": 2, "message": "583", "line": 126, "column": 15, "nodeType": null, "messageId": "483", "endLine": 126, "endColumn": 29}, {"ruleId": "554", "severity": 1, "message": "584", "line": 166, "column": 6, "nodeType": "556", "endLine": 166, "endColumn": 49, "suggestions": "585"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 400, "column": 84, "nodeType": "478", "messageId": "479", "endLine": 400, "endColumn": 87, "suggestions": "586"}, {"ruleId": "481", "severity": 2, "message": "571", "line": 24, "column": 3, "nodeType": null, "messageId": "483", "endLine": 24, "endColumn": 8}, {"ruleId": "481", "severity": 2, "message": "565", "line": 26, "column": 3, "nodeType": null, "messageId": "483", "endLine": 26, "endColumn": 8}, {"ruleId": "481", "severity": 2, "message": "566", "line": 27, "column": 3, "nodeType": null, "messageId": "483", "endLine": 27, "endColumn": 14}, {"ruleId": "481", "severity": 2, "message": "567", "line": 28, "column": 3, "nodeType": null, "messageId": "483", "endLine": 28, "endColumn": 10}, {"ruleId": "476", "severity": 2, "message": "477", "line": 321, "column": 110, "nodeType": "478", "messageId": "479", "endLine": 321, "endColumn": 113, "suggestions": "587"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 336, "column": 104, "nodeType": "478", "messageId": "479", "endLine": 336, "endColumn": 107, "suggestions": "588"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 409, "column": 104, "nodeType": "478", "messageId": "479", "endLine": 409, "endColumn": 107, "suggestions": "589"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 425, "column": 99, "nodeType": "478", "messageId": "479", "endLine": 425, "endColumn": 102, "suggestions": "590"}, {"ruleId": "481", "severity": 2, "message": "550", "line": 22, "column": 3, "nodeType": null, "messageId": "483", "endLine": 22, "endColumn": 11}, {"ruleId": "481", "severity": 2, "message": "581", "line": 23, "column": 3, "nodeType": null, "messageId": "483", "endLine": 23, "endColumn": 7}, {"ruleId": "554", "severity": 1, "message": "591", "line": 175, "column": 6, "nodeType": "556", "endLine": 175, "endColumn": 93, "suggestions": "592"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 292, "column": 85, "nodeType": "478", "messageId": "479", "endLine": 292, "endColumn": 88, "suggestions": "593"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 309, "column": 83, "nodeType": "478", "messageId": "479", "endLine": 309, "endColumn": 86, "suggestions": "594"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 35, "column": 20, "nodeType": "478", "messageId": "479", "endLine": 35, "endColumn": 23, "suggestions": "595"}, {"ruleId": "481", "severity": 2, "message": "568", "line": 67, "column": 5, "nodeType": null, "messageId": "483", "endLine": 67, "endColumn": 10}, {"ruleId": "554", "severity": 1, "message": "596", "line": 97, "column": 6, "nodeType": "556", "endLine": 97, "endColumn": 47, "suggestions": "597"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 148, "column": 47, "nodeType": "560", "messageId": "561", "suggestions": "598"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 220, "column": 79, "nodeType": "478", "messageId": "479", "endLine": 220, "endColumn": 82, "suggestions": "599"}, {"ruleId": "481", "severity": 2, "message": "600", "line": 12, "column": 43, "nodeType": null, "messageId": "483", "endLine": 12, "endColumn": 56}, {"ruleId": "554", "severity": 1, "message": "601", "line": 134, "column": 6, "nodeType": "556", "endLine": 134, "endColumn": 62, "suggestions": "602"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 178, "column": 55, "nodeType": "478", "messageId": "479", "endLine": 178, "endColumn": 58, "suggestions": "603"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 301, "column": 80, "nodeType": "478", "messageId": "479", "endLine": 301, "endColumn": 83, "suggestions": "604"}, {"ruleId": "481", "severity": 2, "message": "605", "line": 8, "column": 10, "nodeType": null, "messageId": "483", "endLine": 8, "endColumn": 14}, {"ruleId": "481", "severity": 2, "message": "606", "line": 8, "column": 16, "nodeType": null, "messageId": "483", "endLine": 8, "endColumn": 27}, {"ruleId": "481", "severity": 2, "message": "607", "line": 8, "column": 29, "nodeType": null, "messageId": "483", "endLine": 8, "endColumn": 44}, {"ruleId": "481", "severity": 2, "message": "608", "line": 8, "column": 46, "nodeType": null, "messageId": "483", "endLine": 8, "endColumn": 56}, {"ruleId": "481", "severity": 2, "message": "609", "line": 8, "column": 58, "nodeType": null, "messageId": "483", "endLine": 8, "endColumn": 67}, {"ruleId": "476", "severity": 2, "message": "477", "line": 32, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 32, "endColumn": 13, "suggestions": "610"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 39, "column": 45, "nodeType": "478", "messageId": "479", "endLine": 39, "endColumn": 48, "suggestions": "611"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 40, "column": 39, "nodeType": "478", "messageId": "479", "endLine": 40, "endColumn": 42, "suggestions": "612"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 54, "column": 65, "nodeType": "478", "messageId": "479", "endLine": 54, "endColumn": 68, "suggestions": "613"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 57, "column": 49, "nodeType": "478", "messageId": "479", "endLine": 57, "endColumn": 52, "suggestions": "614"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 73, "column": 29, "nodeType": "478", "messageId": "479", "endLine": 73, "endColumn": 32, "suggestions": "615"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 85, "column": 29, "nodeType": "478", "messageId": "479", "endLine": 85, "endColumn": 32, "suggestions": "616"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 101, "column": 31, "nodeType": "478", "messageId": "479", "endLine": 101, "endColumn": 34, "suggestions": "617"}, {"ruleId": "476", "severity": 2, "message": "477", "line": 107, "column": 37, "nodeType": "478", "messageId": "479", "endLine": 107, "endColumn": 40, "suggestions": "618"}, {"ruleId": "481", "severity": 2, "message": "619", "line": 21, "column": 3, "nodeType": null, "messageId": "483", "endLine": 21, "endColumn": 9}, {"ruleId": "476", "severity": 2, "message": "477", "line": 32, "column": 29, "nodeType": "478", "messageId": "479", "endLine": 32, "endColumn": 32, "suggestions": "620"}, {"ruleId": "554", "severity": 1, "message": "621", "line": 88, "column": 6, "nodeType": "556", "endLine": 88, "endColumn": 37, "suggestions": "622"}, {"ruleId": "558", "severity": 2, "message": "623", "line": 178, "column": 34, "nodeType": "560", "messageId": "561", "suggestions": "624"}, {"ruleId": "558", "severity": 2, "message": "623", "line": 178, "column": 42, "nodeType": "560", "messageId": "561", "suggestions": "625"}, "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["626"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["627", "628"], "@typescript-eslint/no-unused-vars", "'updateAnnouncementSchema' is assigned a value but never used.", "unusedVar", "prefer-const", "'where' is never reassigned. Use 'const' instead.", "useConst", {"range": "629", "text": "630"}, ["631", "632"], "'skip' is assigned a value but never used.", {"range": "633", "text": "630"}, ["634", "635"], "'prisma' is defined but never used.", "'submitResultsSchema' is assigned a value but never used.", "'request' is defined but never used.", "'params' is defined but never used.", "'where<PERSON><PERSON><PERSON>' is never reassigned. Use 'const' instead.", {"range": "636", "text": "637"}, ["638", "639"], ["640", "641"], ["642", "643"], ["644", "645"], ["646", "647"], ["648", "649"], ["650", "651"], ["652", "653"], ["654", "655"], "'updateAttendanceSchema' is assigned a value but never used.", {"range": "656", "text": "657"}, ["658", "659"], ["660", "661"], "'updatedRecord' is assigned a value but never used.", ["662", "663"], ["664", "665"], ["666", "667"], ["668", "669"], "'fields' is defined but never used.", ["670", "671"], ["672", "673"], ["674", "675"], ["676", "677"], ["678", "679"], ["680", "681"], ["682", "683"], ["684", "685"], ["686", "687"], "'gradeAssessmentSchema' is assigned a value but never used.", "'updateLeadSchema' is assigned a value but never used.", "'callActionSchema' is assigned a value but never used.", "'updateMessageSchema' is assigned a value but never used.", {"range": "688", "text": "630"}, ["689", "690"], "'sendMessageSchema' is assigned a value but never used.", ["691", "692"], ["693", "694"], ["695", "696"], "'globalSearchSchema' is assigned a value but never used.", ["697", "698"], "'getServerSession' is defined but never used.", "'authOptions' is defined but never used.", "'syncRequestSchema' is assigned a value but never used.", {"range": "699", "text": "700"}, ["701", "702"], "'api<PERSON>ey' is assigned a value but never used.", {"range": "703", "text": "657"}, ["704", "705"], "'password' is defined but never used.", "'password' is assigned a value but never used.", ["706", "707"], "'error' is defined but never used.", "'Calendar' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", ["708", "709"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchAnalytics'. Either include it or remove the dependency array.", "ArrayExpression", ["710"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["711", "712", "713", "714"], "'Textarea' is defined but never used.", "'FileText' is defined but never used.", "'Clock' is defined but never used.", "'CheckCircle' is defined but never used.", "'XCircle' is defined but never used.", "'watch' is assigned a value but never used.", ["715", "716"], ["717", "718"], "'Users' is defined but never used.", "'statusColors' is assigned a value but never used.", "'statusIcons' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAttendanceRecords'. Either include it or remove the dependency array.", ["719"], ["720", "721"], ["722", "723"], ["724", "725"], "React Hook useEffect has a missing dependency: 'fetchGroups'. Either include it or remove the dependency array.", ["726"], "'User' is defined but never used.", "'MessageSquare' is defined but never used.", "'setValueCreate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLeads'. Either include it or remove the dependency array.", ["727"], ["728", "729"], ["730", "731"], ["732", "733"], ["734", "735"], ["736", "737"], "React Hook useEffect has a missing dependency: 'fetchPayments'. Either include it or remove the dependency array.", ["738"], ["739", "740"], ["741", "742"], ["743", "744"], "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["745"], ["746", "747", "748", "749"], ["750", "751"], "'GraduationCap' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchStudents'. Either include it or remove the dependency array.", ["752"], ["753", "754"], ["755", "756"], "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardDescription' is defined but never used.", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", ["757", "758"], ["759", "760"], ["761", "762"], ["763", "764"], ["765", "766"], ["767", "768"], ["769", "770"], ["771", "772"], ["773", "774"], "'Filter' is defined but never used.", ["775", "776"], "React Hook useEffect has a missing dependency: 'performSearch'. Either include it or remove the dependency array.", ["777"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["778", "779", "780", "781"], ["782", "783", "784", "785"], {"messageId": "786", "fix": "787", "desc": "788"}, {"messageId": "789", "fix": "790", "desc": "791"}, {"messageId": "792", "fix": "793", "desc": "794"}, [1522, 1541], "const where: any = {}", {"messageId": "789", "fix": "795", "desc": "791"}, {"messageId": "792", "fix": "796", "desc": "794"}, [1713, 1732], {"messageId": "789", "fix": "797", "desc": "791"}, {"messageId": "792", "fix": "798", "desc": "794"}, [913, 974], "const whereClause: any = {\n      date: { gte: startDate }\n    }", {"messageId": "789", "fix": "799", "desc": "791"}, {"messageId": "792", "fix": "800", "desc": "794"}, {"messageId": "789", "fix": "801", "desc": "791"}, {"messageId": "792", "fix": "802", "desc": "794"}, {"messageId": "789", "fix": "803", "desc": "791"}, {"messageId": "792", "fix": "804", "desc": "794"}, {"messageId": "789", "fix": "805", "desc": "791"}, {"messageId": "792", "fix": "806", "desc": "794"}, {"messageId": "789", "fix": "807", "desc": "791"}, {"messageId": "792", "fix": "808", "desc": "794"}, {"messageId": "789", "fix": "809", "desc": "791"}, {"messageId": "792", "fix": "810", "desc": "794"}, {"messageId": "789", "fix": "811", "desc": "791"}, {"messageId": "792", "fix": "812", "desc": "794"}, {"messageId": "789", "fix": "813", "desc": "791"}, {"messageId": "792", "fix": "814", "desc": "794"}, {"messageId": "789", "fix": "815", "desc": "791"}, {"messageId": "792", "fix": "816", "desc": "794"}, [1677, 1702], "const whereClause: any = {}", {"messageId": "789", "fix": "817", "desc": "791"}, {"messageId": "792", "fix": "818", "desc": "794"}, {"messageId": "789", "fix": "819", "desc": "791"}, {"messageId": "792", "fix": "820", "desc": "794"}, {"messageId": "789", "fix": "821", "desc": "791"}, {"messageId": "792", "fix": "822", "desc": "794"}, {"messageId": "789", "fix": "823", "desc": "791"}, {"messageId": "792", "fix": "824", "desc": "794"}, {"messageId": "789", "fix": "825", "desc": "791"}, {"messageId": "792", "fix": "826", "desc": "794"}, {"messageId": "789", "fix": "827", "desc": "791"}, {"messageId": "792", "fix": "828", "desc": "794"}, {"messageId": "789", "fix": "829", "desc": "791"}, {"messageId": "792", "fix": "830", "desc": "794"}, {"messageId": "789", "fix": "831", "desc": "791"}, {"messageId": "792", "fix": "832", "desc": "794"}, {"messageId": "789", "fix": "833", "desc": "791"}, {"messageId": "792", "fix": "834", "desc": "794"}, {"messageId": "789", "fix": "835", "desc": "791"}, {"messageId": "792", "fix": "836", "desc": "794"}, {"messageId": "789", "fix": "837", "desc": "791"}, {"messageId": "792", "fix": "838", "desc": "794"}, {"messageId": "789", "fix": "839", "desc": "791"}, {"messageId": "792", "fix": "840", "desc": "794"}, {"messageId": "789", "fix": "841", "desc": "791"}, {"messageId": "792", "fix": "842", "desc": "794"}, {"messageId": "789", "fix": "843", "desc": "791"}, {"messageId": "792", "fix": "844", "desc": "794"}, {"messageId": "789", "fix": "845", "desc": "791"}, {"messageId": "792", "fix": "846", "desc": "794"}, [1639, 1658], {"messageId": "789", "fix": "847", "desc": "791"}, {"messageId": "792", "fix": "848", "desc": "794"}, {"messageId": "789", "fix": "849", "desc": "791"}, {"messageId": "792", "fix": "850", "desc": "794"}, {"messageId": "789", "fix": "851", "desc": "791"}, {"messageId": "792", "fix": "852", "desc": "794"}, {"messageId": "789", "fix": "853", "desc": "791"}, {"messageId": "792", "fix": "854", "desc": "794"}, {"messageId": "789", "fix": "855", "desc": "791"}, {"messageId": "792", "fix": "856", "desc": "794"}, [1499, 1642], "const whereClause: any = {\n      isActive: true,\n      OR: [\n        { expiresAt: null },\n        { expiresAt: { gt: new Date() } }\n      ]\n    }", {"messageId": "789", "fix": "857", "desc": "791"}, {"messageId": "792", "fix": "858", "desc": "794"}, [969, 994], {"messageId": "789", "fix": "859", "desc": "791"}, {"messageId": "792", "fix": "860", "desc": "794"}, {"messageId": "789", "fix": "861", "desc": "791"}, {"messageId": "792", "fix": "862", "desc": "794"}, {"messageId": "789", "fix": "863", "desc": "791"}, {"messageId": "792", "fix": "864", "desc": "794"}, {"desc": "865", "fix": "866"}, {"messageId": "867", "data": "868", "fix": "869", "desc": "870"}, {"messageId": "867", "data": "871", "fix": "872", "desc": "873"}, {"messageId": "867", "data": "874", "fix": "875", "desc": "876"}, {"messageId": "867", "data": "877", "fix": "878", "desc": "879"}, {"messageId": "789", "fix": "880", "desc": "791"}, {"messageId": "792", "fix": "881", "desc": "794"}, {"messageId": "789", "fix": "882", "desc": "791"}, {"messageId": "792", "fix": "883", "desc": "794"}, {"desc": "884", "fix": "885"}, {"messageId": "789", "fix": "886", "desc": "791"}, {"messageId": "792", "fix": "887", "desc": "794"}, {"messageId": "789", "fix": "888", "desc": "791"}, {"messageId": "792", "fix": "889", "desc": "794"}, {"messageId": "789", "fix": "890", "desc": "791"}, {"messageId": "792", "fix": "891", "desc": "794"}, {"desc": "892", "fix": "893"}, {"desc": "894", "fix": "895"}, {"messageId": "789", "fix": "896", "desc": "791"}, {"messageId": "792", "fix": "897", "desc": "794"}, {"messageId": "789", "fix": "898", "desc": "791"}, {"messageId": "792", "fix": "899", "desc": "794"}, {"messageId": "789", "fix": "900", "desc": "791"}, {"messageId": "792", "fix": "901", "desc": "794"}, {"messageId": "789", "fix": "902", "desc": "791"}, {"messageId": "792", "fix": "903", "desc": "794"}, {"messageId": "789", "fix": "904", "desc": "791"}, {"messageId": "792", "fix": "905", "desc": "794"}, {"desc": "906", "fix": "907"}, {"messageId": "789", "fix": "908", "desc": "791"}, {"messageId": "792", "fix": "909", "desc": "794"}, {"messageId": "789", "fix": "910", "desc": "791"}, {"messageId": "792", "fix": "911", "desc": "794"}, {"messageId": "789", "fix": "912", "desc": "791"}, {"messageId": "792", "fix": "913", "desc": "794"}, {"desc": "914", "fix": "915"}, {"messageId": "867", "data": "916", "fix": "917", "desc": "870"}, {"messageId": "867", "data": "918", "fix": "919", "desc": "873"}, {"messageId": "867", "data": "920", "fix": "921", "desc": "876"}, {"messageId": "867", "data": "922", "fix": "923", "desc": "879"}, {"messageId": "789", "fix": "924", "desc": "791"}, {"messageId": "792", "fix": "925", "desc": "794"}, {"desc": "926", "fix": "927"}, {"messageId": "789", "fix": "928", "desc": "791"}, {"messageId": "792", "fix": "929", "desc": "794"}, {"messageId": "789", "fix": "930", "desc": "791"}, {"messageId": "792", "fix": "931", "desc": "794"}, {"messageId": "789", "fix": "932", "desc": "791"}, {"messageId": "792", "fix": "933", "desc": "794"}, {"messageId": "789", "fix": "934", "desc": "791"}, {"messageId": "792", "fix": "935", "desc": "794"}, {"messageId": "789", "fix": "936", "desc": "791"}, {"messageId": "792", "fix": "937", "desc": "794"}, {"messageId": "789", "fix": "938", "desc": "791"}, {"messageId": "792", "fix": "939", "desc": "794"}, {"messageId": "789", "fix": "940", "desc": "791"}, {"messageId": "792", "fix": "941", "desc": "794"}, {"messageId": "789", "fix": "942", "desc": "791"}, {"messageId": "792", "fix": "943", "desc": "794"}, {"messageId": "789", "fix": "944", "desc": "791"}, {"messageId": "792", "fix": "945", "desc": "794"}, {"messageId": "789", "fix": "946", "desc": "791"}, {"messageId": "792", "fix": "947", "desc": "794"}, {"messageId": "789", "fix": "948", "desc": "791"}, {"messageId": "792", "fix": "949", "desc": "794"}, {"messageId": "789", "fix": "950", "desc": "791"}, {"messageId": "792", "fix": "951", "desc": "794"}, {"desc": "952", "fix": "953"}, {"messageId": "867", "data": "954", "fix": "955", "desc": "956"}, {"messageId": "867", "data": "957", "fix": "958", "desc": "959"}, {"messageId": "867", "data": "960", "fix": "961", "desc": "962"}, {"messageId": "867", "data": "963", "fix": "964", "desc": "965"}, {"messageId": "867", "data": "966", "fix": "967", "desc": "956"}, {"messageId": "867", "data": "968", "fix": "969", "desc": "959"}, {"messageId": "867", "data": "970", "fix": "971", "desc": "962"}, {"messageId": "867", "data": "972", "fix": "973", "desc": "965"}, "replaceEmptyInterfaceWithSuper", {"range": "974", "text": "975"}, "Replace empty interface with a type alias.", "suggestUnknown", {"range": "976", "text": "977"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "978", "text": "979"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "980", "text": "977"}, {"range": "981", "text": "979"}, {"range": "982", "text": "977"}, {"range": "983", "text": "979"}, {"range": "984", "text": "977"}, {"range": "985", "text": "979"}, {"range": "986", "text": "977"}, {"range": "987", "text": "979"}, {"range": "988", "text": "977"}, {"range": "989", "text": "979"}, {"range": "990", "text": "977"}, {"range": "991", "text": "979"}, {"range": "992", "text": "977"}, {"range": "993", "text": "979"}, {"range": "994", "text": "977"}, {"range": "995", "text": "979"}, {"range": "996", "text": "977"}, {"range": "997", "text": "979"}, {"range": "998", "text": "977"}, {"range": "999", "text": "979"}, {"range": "1000", "text": "977"}, {"range": "1001", "text": "979"}, {"range": "1002", "text": "977"}, {"range": "1003", "text": "979"}, {"range": "1004", "text": "977"}, {"range": "1005", "text": "979"}, {"range": "1006", "text": "977"}, {"range": "1007", "text": "979"}, {"range": "1008", "text": "977"}, {"range": "1009", "text": "979"}, {"range": "1010", "text": "977"}, {"range": "1011", "text": "979"}, {"range": "1012", "text": "977"}, {"range": "1013", "text": "979"}, {"range": "1014", "text": "977"}, {"range": "1015", "text": "979"}, {"range": "1016", "text": "977"}, {"range": "1017", "text": "979"}, {"range": "1018", "text": "977"}, {"range": "1019", "text": "979"}, {"range": "1020", "text": "977"}, {"range": "1021", "text": "979"}, {"range": "1022", "text": "977"}, {"range": "1023", "text": "979"}, {"range": "1024", "text": "977"}, {"range": "1025", "text": "979"}, {"range": "1026", "text": "977"}, {"range": "1027", "text": "979"}, {"range": "1028", "text": "977"}, {"range": "1029", "text": "979"}, {"range": "1030", "text": "977"}, {"range": "1031", "text": "979"}, {"range": "1032", "text": "977"}, {"range": "1033", "text": "979"}, {"range": "1034", "text": "977"}, {"range": "1035", "text": "979"}, {"range": "1036", "text": "977"}, {"range": "1037", "text": "979"}, {"range": "1038", "text": "977"}, {"range": "1039", "text": "979"}, {"range": "1040", "text": "977"}, {"range": "1041", "text": "979"}, {"range": "1042", "text": "977"}, {"range": "1043", "text": "979"}, {"range": "1044", "text": "977"}, {"range": "1045", "text": "979"}, {"range": "1046", "text": "977"}, {"range": "1047", "text": "979"}, {"range": "1048", "text": "977"}, {"range": "1049", "text": "979"}, "Update the dependencies array to be: [period, branch, fetchAnalytics]", {"range": "1050", "text": "1051"}, "replaceWithAlt", {"alt": "1052"}, {"range": "1053", "text": "1054"}, "Replace with `&apos;`.", {"alt": "1055"}, {"range": "1056", "text": "1057"}, "Replace with `&lsquo;`.", {"alt": "1058"}, {"range": "1059", "text": "1060"}, "Replace with `&#39;`.", {"alt": "1061"}, {"range": "1062", "text": "1063"}, "Replace with `&rsquo;`.", {"range": "1064", "text": "977"}, {"range": "1065", "text": "979"}, {"range": "1066", "text": "977"}, {"range": "1067", "text": "979"}, "Update the dependencies array to be: [pagination.page, groupFilter, dateFromFilter, dateToFilter, fetchAttendanceRecords]", {"range": "1068", "text": "1069"}, {"range": "1070", "text": "977"}, {"range": "1071", "text": "979"}, {"range": "1072", "text": "977"}, {"range": "1073", "text": "979"}, {"range": "1074", "text": "977"}, {"range": "1075", "text": "979"}, "Update the dependencies array to be: [pagination.page, searchTerm, courseFilter, fetchGroups]", {"range": "1076", "text": "1077"}, "Update the dependencies array to be: [fetchLeads, pagination.page, searchTerm, statusFilter]", {"range": "1078", "text": "1079"}, {"range": "1080", "text": "977"}, {"range": "1081", "text": "979"}, {"range": "1082", "text": "977"}, {"range": "1083", "text": "979"}, {"range": "1084", "text": "977"}, {"range": "1085", "text": "979"}, {"range": "1086", "text": "977"}, {"range": "1087", "text": "979"}, {"range": "1088", "text": "977"}, {"range": "1089", "text": "979"}, "Update the dependencies array to be: [pagination.page, searchTerm, statusFilter, methodFilter, dateFromFilter, dateToFilter, fetchPayments]", {"range": "1090", "text": "1091"}, {"range": "1092", "text": "977"}, {"range": "1093", "text": "979"}, {"range": "1094", "text": "977"}, {"range": "1095", "text": "979"}, {"range": "1096", "text": "977"}, {"range": "1097", "text": "979"}, "Update the dependencies array to be: [pagination.page, searchTerm, roleFilter, fetchUsers]", {"range": "1098", "text": "1099"}, {"alt": "1052"}, {"range": "1100", "text": "1101"}, {"alt": "1055"}, {"range": "1102", "text": "1103"}, {"alt": "1058"}, {"range": "1104", "text": "1105"}, {"alt": "1061"}, {"range": "1106", "text": "1107"}, {"range": "1108", "text": "977"}, {"range": "1109", "text": "979"}, "Update the dependencies array to be: [pagination.page, searchTerm, statusFilter, levelFilter, fetchStudents]", {"range": "1110", "text": "1111"}, {"range": "1112", "text": "977"}, {"range": "1113", "text": "979"}, {"range": "1114", "text": "977"}, {"range": "1115", "text": "979"}, {"range": "1116", "text": "977"}, {"range": "1117", "text": "979"}, {"range": "1118", "text": "977"}, {"range": "1119", "text": "979"}, {"range": "1120", "text": "977"}, {"range": "1121", "text": "979"}, {"range": "1122", "text": "977"}, {"range": "1123", "text": "979"}, {"range": "1124", "text": "977"}, {"range": "1125", "text": "979"}, {"range": "1126", "text": "977"}, {"range": "1127", "text": "979"}, {"range": "1128", "text": "977"}, {"range": "1129", "text": "979"}, {"range": "1130", "text": "977"}, {"range": "1131", "text": "979"}, {"range": "1132", "text": "977"}, {"range": "1133", "text": "979"}, {"range": "1134", "text": "977"}, {"range": "1135", "text": "979"}, "Update the dependencies array to be: [debounced<PERSON><PERSON><PERSON>, performSearch, selectedTypes]", {"range": "1136", "text": "1137"}, {"alt": "1138"}, {"range": "1139", "text": "1140"}, "Replace with `&quot;`.", {"alt": "1141"}, {"range": "1142", "text": "1143"}, "Replace with `&ldquo;`.", {"alt": "1144"}, {"range": "1145", "text": "1146"}, "Replace with `&#34;`.", {"alt": "1147"}, {"range": "1148", "text": "1149"}, "Replace with `&rdquo;`.", {"alt": "1138"}, {"range": "1150", "text": "1151"}, {"alt": "1141"}, {"range": "1152", "text": "1153"}, {"alt": "1144"}, {"range": "1154", "text": "1155"}, {"alt": "1147"}, {"range": "1156", "text": "1157"}, [75, 152], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [1756, 1759], "unknown", [1756, 1759], "never", [1533, 1536], [1533, 1536], [1724, 1727], [1724, 1727], [930, 933], [930, 933], [3146, 3149], [3146, 3149], [3252, 3255], [3252, 3255], [5138, 5141], [5138, 5141], [5146, 5149], [5146, 5149], [6143, 6146], [6143, 6146], [6341, 6344], [6341, 6344], [6426, 6429], [6426, 6429], [6434, 6437], [6434, 6437], [1694, 1697], [1694, 1697], [7013, 7016], [7013, 7016], [4423, 4426], [4423, 4426], [5959, 5962], [5959, 5962], [1010, 1013], [1010, 1013], [3889, 3892], [3889, 3892], [5010, 5013], [5010, 5013], [5551, 5554], [5551, 5554], [6080, 6083], [6080, 6083], [6882, 6885], [6882, 6885], [7572, 7575], [7572, 7575], [8464, 8467], [8464, 8467], [9467, 9470], [9467, 9470], [9519, 9522], [9519, 9522], [9735, 9738], [9735, 9738], [1650, 1653], [1650, 1653], [4931, 4934], [4931, 4934], [3062, 3065], [3062, 3065], [4498, 4501], [4498, 4501], [687, 690], [687, 690], [1516, 1519], [1516, 1519], [986, 989], [986, 989], [2816, 2819], [2816, 2819], [1844, 1847], [1844, 1847], [2860, 2876], "[period, branch, fetchAnalytics]", "&apos;", [3229, 3273], "You don&apos;t have permission to view analytics.", "&lsquo;", [3229, 3273], "You don&lsquo;t have permission to view analytics.", "&#39;", [3229, 3273], "You don&#39;t have permission to view analytics.", "&rsquo;", [3229, 3273], "You don&rsquo;t have permission to view analytics.", [10788, 10791], [10788, 10791], [11682, 11685], [11682, 11685], [5455, 5515], "[pagination.page, groupFilter, dateFrom<PERSON><PERSON>er, dateTo<PERSON><PERSON>er, fetchAttendanceRecords]", [1600, 1603], [1600, 1603], [4024, 4027], [4024, 4027], [4097, 4100], [4097, 4100], [4382, 4425], "[pagination.page, searchTerm, courseFilter, fetchGroups]", [4619, 4662], "[fetchLeads, pagination.page, searchTerm, statusFilter]", [12461, 12464], [12461, 12464], [10739, 10742], [10739, 10742], [11556, 11559], [11556, 11559], [14870, 14873], [14870, 14873], [15767, 15770], [15767, 15770], [4652, 4739], "[pagination.page, searchTerm, statusFilter, methodFilter, dateFromFilter, dateToFilter, fetchPayments]", [9045, 9048], [9045, 9048], [9882, 9885], [9882, 9885], [1462, 1465], [1462, 1465], [2994, 3035], "[pagination.page, searchTerm, roleFilter, fetchUsers]", [4407, 4453], "You don&apos;t have permission to access this page.", [4407, 4453], "You don&lsquo;t have permission to access this page.", [4407, 4453], "You don&#39;t have permission to access this page.", [4407, 4453], "You don&rsquo;t have permission to access this page.", [7148, 7151], [7148, 7151], [3912, 3968], "[pagination.page, searchTerm, statusFilter, levelFilter, fetchStudents]", [5104, 5107], [5104, 5107], [9036, 9039], [9036, 9039], [1050, 1053], [1050, 1053], [1237, 1240], [1237, 1240], [1289, 1292], [1289, 1292], [1710, 1713], [1710, 1713], [1827, 1830], [1827, 1830], [2369, 2372], [2369, 2372], [2709, 2712], [2709, 2712], [3078, 3081], [3078, 3081], [3197, 3200], [3197, 3200], [790, 793], [790, 793], [2243, 2274], "[debounced<PERSON><PERSON><PERSON>, performSearch, selectedTypes]", "&quot;", [4818, 4853], "\n            No results found for &quot;", "&ldquo;", [4818, 4853], "\n            No results found for &ldquo;", "&#34;", [4818, 4853], "\n            No results found for &#34;", "&rdquo;", [4818, 4853], "\n            No results found for &rdquo;", [4860, 4872], "&quot;\n          ", [4860, 4872], "&ldquo;\n          ", [4860, 4872], "&#34;\n          ", [4860, 4872], "&rdquo;\n          "]