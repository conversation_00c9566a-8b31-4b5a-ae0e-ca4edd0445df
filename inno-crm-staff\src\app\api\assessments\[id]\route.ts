import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const updateAssessmentSchema = z.object({
  testName: z.string().min(1, "Test name is required").optional(),
  type: z.enum(["LEVEL_TEST", "PROGRESS_TEST", "FINAL_EXAM", "GROUP_TEST"]).optional(),
  level: z.enum(["A1", "A2", "B1", "B2", "IELTS", "SAT", "MATH", "KIDS"]).optional(),
  maxScore: z.number().min(1, "Max score must be at least 1").optional(),
  questions: z.array(z.object({
    id: z.string(),
    question: z.string(),
    type: z.enum(["multiple_choice", "text", "essay", "listening", "speaking"]),
    options: z.array(z.string()).optional(),
    correctAnswer: z.string().optional(),
    points: z.number().default(1)
  })).optional(),
})

const submitResultsSchema = z.object({
  score: z.number().min(0, "Score must be non-negative"),
  passed: z.boolean().optional(),
  results: z.record(z.any()).optional(), // Flexible results object
  answers: z.array(z.object({
    questionId: z.string(),
    answer: z.string(),
    isCorrect: z.boolean().optional(),
    points: z.number().optional()
  })).optional(),
  completedAt: z.string().optional().transform((str) => str ? new Date(str) : new Date()),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // TODO: Fetch assessment from students server via inter-server API
    // For now, we'll return a mock assessment
    const assessment = {
      id: params.id,
      studentReferenceId: "student_123",
      groupId: "group_123",
      testName: "Level Assessment A1",
      type: "LEVEL_TEST",
      level: "A1",
      score: null,
      maxScore: 100,
      passed: false,
      questions: [],
      results: null,
      assignedBy: "teacher_123",
      assignedAt: new Date(),
      startedAt: null,
      completedAt: null,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    return NextResponse.json(assessment)
  } catch (error) {
    console.error("Error fetching assessment:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "TEACHER", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updateAssessmentSchema.parse(body)

    // TODO: Update assessment via inter-server API call to students server
    // For now, we'll simulate the response
    const updatedAssessment = {
      id: params.id,
      ...validatedData,
      updatedAt: new Date()
    }

    return NextResponse.json(updatedAssessment)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error updating assessment:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // TODO: Delete assessment via inter-server API call to students server
    // For now, we'll simulate the response
    
    return NextResponse.json({ message: "Assessment deleted successfully" })
  } catch (error) {
    console.error("Error deleting assessment:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
