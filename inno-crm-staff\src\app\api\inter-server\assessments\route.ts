import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { z } from "zod"

const STUDENTS_SERVER_URL = process.env.STUDENTS_SERVER_URL || "http://localhost:3001"
const INTER_SERVER_SECRET = process.env.INTER_SERVER_SECRET || "your-secret-key"

const createAssessmentSchema = z.object({
  studentId: z.string().min(1, "Student ID is required"),
  groupReferenceId: z.string().optional(),
  testName: z.string().min(1, "Test name is required"),
  type: z.enum(["LEVEL_TEST", "PROGRESS_TEST", "FINAL_EXAM", "GROUP_TEST"]),
  level: z.enum(["A1", "A2", "B1", "B2", "IELTS", "SAT", "MATH", "KIDS"]).optional(),
  maxScore: z.number().min(1, "Max score must be at least 1").default(100),
  questions: z.array(z.object({
    id: z.string(),
    question: z.string(),
    type: z.enum(["multiple_choice", "text", "essay", "listening", "speaking"]),
    options: z.array(z.string()).optional(),
    correctAnswer: z.string().optional(),
    points: z.number().default(1)
  })).optional(),
  assignedBy: z.string().optional(),
  assignedAt: z.string().optional(),
})

const gradeAssessmentSchema = z.object({
  score: z.number().min(0, "Score must be non-negative"),
  passed: z.boolean().optional(),
  results: z.record(z.any()).optional(),
  feedback: z.string().optional(),
  gradedBy: z.string().optional(),
  gradedAt: z.string().optional(),
})

// Helper function to make authenticated requests to students server
async function makeStudentsServerRequest(
  endpoint: string,
  options: RequestInit = {}
) {
  const url = `${STUDENTS_SERVER_URL}/api/inter-server${endpoint}`
  
  const response = await fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      'Authorization': `Bearer ${INTER_SERVER_SECRET}`,
      'Content-Type': 'application/json',
    },
  })

  if (!response.ok) {
    throw new Error(`Students server request failed: ${response.status}`)
  }

  return response.json()
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const studentId = searchParams.get("studentId")
    const groupId = searchParams.get("groupId")
    const type = searchParams.get("type")
    const level = searchParams.get("level")
    const status = searchParams.get("status")
    const page = searchParams.get("page") || "1"
    const limit = searchParams.get("limit") || "10"

    // Build query parameters for students server
    const queryParams = new URLSearchParams()
    if (studentId) queryParams.append("studentId", studentId)
    if (groupId) queryParams.append("groupReferenceId", groupId)
    if (type) queryParams.append("type", type)
    if (level) queryParams.append("level", level)
    if (status) queryParams.append("status", status)
    queryParams.append("page", page)
    queryParams.append("limit", limit)

    const assessments = await makeStudentsServerRequest(
      `/assessments?${queryParams.toString()}`
    )

    return NextResponse.json(assessments)
  } catch (error) {
    console.error("Error fetching assessments from students server:", error)
    return NextResponse.json({ error: "Failed to fetch assessments" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "TEACHER", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createAssessmentSchema.parse(body)

    // Add the assigning user
    const assessmentData = {
      ...validatedData,
      assignedBy: session.user.id,
      assignedAt: new Date().toISOString()
    }

    const assessment = await makeStudentsServerRequest("/assessments", {
      method: "POST",
      body: JSON.stringify(assessmentData)
    })

    return NextResponse.json(assessment, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error creating assessment:", error)
    return NextResponse.json({ error: "Failed to create assessment" }, { status: 500 })
  }
}
