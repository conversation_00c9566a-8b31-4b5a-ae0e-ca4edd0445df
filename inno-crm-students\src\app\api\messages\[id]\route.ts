import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== "STUDENT") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get the student's profile
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { studentProfile: true }
    })

    if (!user?.studentProfile) {
      return NextResponse.json({ error: "Student profile not found" }, { status: 404 })
    }

    const message = await prisma.message.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        subject: true,
        content: true,
        priority: true,
        sentAt: true,
        createdAt: true,
        senderReferenceId: true,
        recipientType: true,
        recipientIds: true,
        status: true,
      }
    })

    if (!message) {
      return NextResponse.json({ error: "Message not found" }, { status: 404 })
    }

    // Check if student has access to this message
    const hasAccess = 
      message.recipientType === "ALL" ||
      message.recipientType === "STUDENTS" ||
      (message.recipientType === "SPECIFIC" && message.recipientIds.includes(user.studentProfile.id))

    if (!hasAccess || message.status !== "SENT") {
      return NextResponse.json({ error: "Message not found" }, { status: 404 })
    }

    return NextResponse.json(message)
  } catch (error) {
    console.error("Error fetching message:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
