import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const updateLeadSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  phone: z.string().min(1, "Phone is required").optional(),
  coursePreference: z.string().min(1, "Course preference is required").optional(),
  status: z.enum(["NEW", "CALLING", "CALL_COMPLETED", "GROUP_ASSIGNED", "ARCHIVED", "NOT_INTERESTED"]).optional(),
  source: z.string().optional(),
  notes: z.string().optional(),
  branch: z.string().optional(),
  assignedTo: z.string().optional(),
  followUpDate: z.string().optional().transform((str) => str ? new Date(str) : undefined),
  assignedGroupId: z.string().optional(),
  assignedTeacherId: z.string().optional(),
})

const callActionSchema = z.object({
  action: z.literal("start_call"),
})

const endCallSchema = z.object({
  action: z.literal("end_call"),
  notes: z.string().optional(),
  outcome: z.enum(["INTERESTED", "NOT_INTERESTED", "CALLBACK", "ENROLLED"]),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const lead = await prisma.lead.findUnique({
      where: { id: params.id },
      include: {
        assignedGroup: {
          include: {
            course: true
          }
        },
        assignedTeacher: {
          include: {
            user: true
          }
        },
        callRecords: {
          orderBy: { createdAt: "desc" },
          include: {
            user: true
          }
        }
      }
    })

    if (!lead) {
      return NextResponse.json({ error: "Lead not found" }, { status: 404 })
    }

    return NextResponse.json(lead)
  } catch (error) {
    console.error("Error fetching lead:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()

    // Handle call actions
    if (body.action === "start_call") {
      const lead = await prisma.lead.findUnique({
        where: { id: params.id }
      })

      if (!lead) {
        return NextResponse.json({ error: "Lead not found" }, { status: 404 })
      }

      // Update lead status and start call
      const updatedLead = await prisma.lead.update({
        where: { id: params.id },
        data: {
          status: "CALLING",
          callStartedAt: new Date(),
          assignedTo: session.user.id
        }
      })

      // Create call record
      await prisma.callRecord.create({
        data: {
          leadId: params.id,
          userId: session.user.id,
          startedAt: new Date()
        }
      })

      return NextResponse.json(updatedLead)
    }

    if (body.action === "end_call") {
      const endCallData = endCallSchema.parse(body)
      const lead = await prisma.lead.findUnique({
        where: { id: params.id }
      })

      if (!lead) {
        return NextResponse.json({ error: "Lead not found" }, { status: 404 })
      }

      const callStartTime = lead.callStartedAt
      const callEndTime = new Date()
      const duration = callStartTime ? Math.floor((callEndTime.getTime() - callStartTime.getTime()) / 1000) : 0

      // Update lead
      const newStatus = endCallData.outcome === "ENROLLED" ? "GROUP_ASSIGNED" : 
                       endCallData.outcome === "NOT_INTERESTED" ? "NOT_INTERESTED" : "CALL_COMPLETED"

      const updatedLead = await prisma.lead.update({
        where: { id: params.id },
        data: {
          status: newStatus,
          callEndedAt: callEndTime,
          callDuration: duration,
          notes: endCallData.notes || lead.notes
        }
      })

      // Update call record
      await prisma.callRecord.updateMany({
        where: {
          leadId: params.id,
          endedAt: null
        },
        data: {
          endedAt: callEndTime,
          duration,
          notes: endCallData.notes
        }
      })

      return NextResponse.json(updatedLead)
    }

    // Regular update
    const validatedData = updateLeadSchema.parse(body)

    // Check if lead exists
    const existingLead = await prisma.lead.findUnique({
      where: { id: params.id }
    })

    if (!existingLead) {
      return NextResponse.json({ error: "Lead not found" }, { status: 404 })
    }

    // Check for phone conflicts if phone is being updated
    if (validatedData.phone && validatedData.phone !== existingLead.phone) {
      const conflictLead = await prisma.lead.findFirst({
        where: {
          AND: [
            { id: { not: params.id } },
            { phone: validatedData.phone }
          ]
        }
      })

      if (conflictLead) {
        return NextResponse.json({ error: "Lead with this phone already exists" }, { status: 400 })
      }
    }

    // Update lead
    const lead = await prisma.lead.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        assignedGroup: {
          include: {
            course: true
          }
        },
        assignedTeacher: {
          include: {
            user: true
          }
        },
        callRecords: {
          orderBy: { createdAt: "desc" },
          take: 5,
          include: {
            user: true
          }
        }
      }
    })

    return NextResponse.json(lead)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error updating lead:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check if lead exists
    const existingLead = await prisma.lead.findUnique({
      where: { id: params.id }
    })

    if (!existingLead) {
      return NextResponse.json({ error: "Lead not found" }, { status: 404 })
    }

    // Delete lead (cascade will handle related records)
    await prisma.lead.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: "Lead deleted successfully" })
  } catch (error) {
    console.error("Error deleting lead:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
