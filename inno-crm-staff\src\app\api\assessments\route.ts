import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const createAssessmentSchema = z.object({
  studentReferenceId: z.string().min(1, "Student is required"),
  groupId: z.string().optional(),
  testName: z.string().min(1, "Test name is required"),
  type: z.enum(["LEVEL_TEST", "PROGRESS_TEST", "FINAL_EXAM", "GROUP_TEST"]),
  level: z.enum(["A1", "A2", "B1", "B2", "IELTS", "SAT", "MATH", "KIDS"]).optional(),
  maxScore: z.number().min(1, "Max score must be at least 1").default(100),
  questions: z.array(z.object({
    id: z.string(),
    question: z.string(),
    type: z.enum(["multiple_choice", "text", "essay", "listening", "speaking"]),
    options: z.array(z.string()).optional(),
    correctAnswer: z.string().optional(),
    points: z.number().default(1)
  })).optional(),
  assignedAt: z.string().optional().transform((str) => str ? new Date(str) : new Date()),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const studentId = searchParams.get("studentId")
    const groupId = searchParams.get("groupId")
    const type = searchParams.get("type")
    const level = searchParams.get("level")
    const branch = searchParams.get("branch")
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const skip = (page - 1) * limit

    let where: any = {}

    if (studentId) {
      where.studentReferenceId = studentId
    }

    if (groupId) {
      where.groupId = groupId
    }

    if (type) {
      where.type = type
    }

    if (level) {
      where.level = level
    }

    if (branch) {
      where.branch = branch
    }

    // For teachers, only show assessments for their groups
    if (session.user.role === "TEACHER") {
      const teacher = await prisma.teacher.findFirst({
        where: { userId: session.user.id }
      })

      if (teacher) {
        const teacherGroups = await prisma.group.findMany({
          where: { teacherId: teacher.id },
          select: { id: true }
        })

        where.groupId = {
          in: teacherGroups.map(g => g.id)
        }
      }
    }

    // Fetch assessments from students server via inter-server API
    const queryParams = new URLSearchParams()
    if (studentId) queryParams.append("studentId", studentId)
    if (groupId) queryParams.append("groupId", groupId)
    if (type) queryParams.append("type", type)
    if (level) queryParams.append("level", level)
    if (branch) queryParams.append("branch", branch)
    queryParams.append("page", page.toString())
    queryParams.append("limit", limit.toString())

    const response = await fetch(`/api/inter-server/assessments?${queryParams.toString()}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    })

    if (!response.ok) {
      throw new Error("Failed to fetch assessments from students server")
    }

    const data = await response.json()
    const assessments = data.assessments || []
    const total = data.pagination?.total || 0

    return NextResponse.json({
      assessments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error("Error fetching assessments:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "TEACHER", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createAssessmentSchema.parse(body)

    // Verify student exists
    const student = await prisma.studentReference.findUnique({
      where: { id: validatedData.studentReferenceId }
    })

    if (!student) {
      return NextResponse.json({ error: "Student not found" }, { status: 404 })
    }

    // Verify group exists if provided
    if (validatedData.groupId) {
      const group = await prisma.group.findUnique({
        where: { id: validatedData.groupId }
      })

      if (!group) {
        return NextResponse.json({ error: "Group not found" }, { status: 404 })
      }

      // For teachers, verify they own the group
      if (session.user.role === "TEACHER") {
        const teacher = await prisma.teacher.findFirst({
          where: { userId: session.user.id }
        })

        if (!teacher || group.teacherId !== teacher.id) {
          return NextResponse.json({ error: "Unauthorized to assign assessment to this group" }, { status: 403 })
        }
      }
    }

    // Create assessment via inter-server API call to students server
    const assessmentData = {
      studentId: validatedData.studentReferenceId,
      groupReferenceId: validatedData.groupId,
      testName: validatedData.testName,
      type: validatedData.type,
      level: validatedData.level,
      maxScore: validatedData.maxScore,
      questions: validatedData.questions,
      assignedBy: session.user.id,
      assignedAt: validatedData.assignedAt.toISOString()
    }

    const response = await fetch("/api/inter-server/assessments", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(assessmentData),
    })

    if (!response.ok) {
      throw new Error("Failed to create assessment on students server")
    }

    const assessment = await response.json()

    return NextResponse.json(assessment, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error creating assessment:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
