"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Plus, Search, Edit, Trash2, Eye, Users, Calendar, Clock, MapPin } from "lucide-react"
import DashboardLayout from "@/components/dashboard/layout"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"

const createGroupSchema = z.object({
  name: z.string().min(1, "Name is required"),
  courseId: z.string().min(1, "Course is required"),
  teacherId: z.string().min(1, "Teacher is required"),
  capacity: z.number().min(1, "Capacity must be at least 1").default(20),
  schedule: z.string().min(1, "Schedule is required"),
  room: z.string().optional(),
  branch: z.string().min(1, "Branch is required"),
  startDate: z.string().min(1, "Start date is required"),
  endDate: z.string().min(1, "End date is required"),
})

type CreateGroupForm = z.infer<typeof createGroupSchema>

interface Group {
  id: string
  name: string
  capacity: number
  schedule: any
  room?: string
  branch: string
  startDate: string
  endDate: string
  isActive: boolean
  course: {
    name: string
    level: string
  }
  teacher: {
    user: {
      name: string
    }
  }
  _count: {
    studentReferences: number
  }
}

interface Course {
  id: string
  name: string
  level: string
}

interface Teacher {
  id: string
  user: {
    name: string
  }
  subject: string
}

export default function GroupsManagementPage() {
  const { data: session } = useSession()
  const [groups, setGroups] = useState<Group[]>([])
  const [courses, setCourses] = useState<Course[]>([])
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [courseFilter, setCourseFilter] = useState("")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  })

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
    watch
  } = useForm<CreateGroupForm>({
    resolver: zodResolver(createGroupSchema),
  })

  const fetchGroups = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(searchTerm && { search: searchTerm }),
        ...(courseFilter && { courseId: courseFilter })
      })

      const response = await fetch(`/api/groups?${params}`)
      if (response.ok) {
        const data = await response.json()
        setGroups(data.groups)
        setPagination(data.pagination)
      }
    } catch (error) {
      console.error("Error fetching groups:", error)
    } finally {
      setLoading(false)
    }
  }

  const fetchCourses = async () => {
    try {
      const response = await fetch("/api/courses?limit=100")
      if (response.ok) {
        const data = await response.json()
        setCourses(data.courses)
      }
    } catch (error) {
      console.error("Error fetching courses:", error)
    }
  }

  const fetchTeachers = async () => {
    try {
      const response = await fetch("/api/users?role=TEACHER&limit=100")
      if (response.ok) {
        const data = await response.json()
        // Map users to teachers format
        const teacherUsers = data.users.filter((user: any) => user.teacherProfile)
        setTeachers(teacherUsers.map((user: any) => ({
          id: user.teacherProfile.id,
          user: { name: user.name },
          subject: user.teacherProfile.subject
        })))
      }
    } catch (error) {
      console.error("Error fetching teachers:", error)
    }
  }

  useEffect(() => {
    fetchGroups()
  }, [pagination.page, searchTerm, courseFilter])

  useEffect(() => {
    fetchCourses()
    fetchTeachers()
  }, [])

  const onSubmit = async (data: CreateGroupForm) => {
    try {
      // Create schedule object
      const scheduleData = {
        days: ["Monday", "Wednesday", "Friday"], // Default schedule
        time: "10:00",
        duration: 90
      }

      const response = await fetch("/api/groups", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          capacity: parseInt(data.capacity.toString()),
          schedule: JSON.stringify(scheduleData)
        }),
      })

      if (response.ok) {
        setIsCreateDialogOpen(false)
        reset()
        fetchGroups()
      } else {
        const error = await response.json()
        console.error("Error creating group:", error)
      }
    } catch (error) {
      console.error("Error creating group:", error)
    }
  }

  const canManageGroups = session?.user?.role && ["ADMIN", "MANAGER", "ACADEMIC_MANAGER"].includes(session.user.role)

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Groups Management</h1>
            <p className="text-gray-600">Manage class groups and schedules</p>
          </div>
          
          {canManageGroups && (
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Group
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Create New Group</DialogTitle>
                  <DialogDescription>
                    Set up a new class group with schedule and assignments.
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">Group Name</Label>
                      <Input
                        id="name"
                        placeholder="e.g., IELTS-A1-Morning"
                        {...register("name")}
                        className={errors.name ? "border-red-500" : ""}
                      />
                      {errors.name && (
                        <p className="text-sm text-red-500 mt-1">{errors.name.message}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="capacity">Capacity</Label>
                      <Input
                        id="capacity"
                        type="number"
                        min="1"
                        defaultValue="20"
                        {...register("capacity", { valueAsNumber: true })}
                        className={errors.capacity ? "border-red-500" : ""}
                      />
                      {errors.capacity && (
                        <p className="text-sm text-red-500 mt-1">{errors.capacity.message}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="courseId">Course</Label>
                    <Select onValueChange={(value) => setValue("courseId", value)}>
                      <SelectTrigger className={errors.courseId ? "border-red-500" : ""}>
                        <SelectValue placeholder="Select course" />
                      </SelectTrigger>
                      <SelectContent>
                        {courses.map((course) => (
                          <SelectItem key={course.id} value={course.id}>
                            {course.name} ({course.level})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.courseId && (
                      <p className="text-sm text-red-500 mt-1">{errors.courseId.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="teacherId">Teacher</Label>
                    <Select onValueChange={(value) => setValue("teacherId", value)}>
                      <SelectTrigger className={errors.teacherId ? "border-red-500" : ""}>
                        <SelectValue placeholder="Select teacher" />
                      </SelectTrigger>
                      <SelectContent>
                        {teachers.map((teacher) => (
                          <SelectItem key={teacher.id} value={teacher.id}>
                            {teacher.user.name} ({teacher.subject})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.teacherId && (
                      <p className="text-sm text-red-500 mt-1">{errors.teacherId.message}</p>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="room">Room</Label>
                      <Input
                        id="room"
                        placeholder="e.g., Room 101"
                        {...register("room")}
                      />
                    </div>

                    <div>
                      <Label htmlFor="branch">Branch</Label>
                      <Select onValueChange={(value) => setValue("branch", value)}>
                        <SelectTrigger className={errors.branch ? "border-red-500" : ""}>
                          <SelectValue placeholder="Select branch" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="main">Main Branch</SelectItem>
                          <SelectItem value="secondary">Secondary Branch</SelectItem>
                        </SelectContent>
                      </Select>
                      {errors.branch && (
                        <p className="text-sm text-red-500 mt-1">{errors.branch.message}</p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="startDate">Start Date</Label>
                      <Input
                        id="startDate"
                        type="date"
                        {...register("startDate")}
                        className={errors.startDate ? "border-red-500" : ""}
                      />
                      {errors.startDate && (
                        <p className="text-sm text-red-500 mt-1">{errors.startDate.message}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="endDate">End Date</Label>
                      <Input
                        id="endDate"
                        type="date"
                        {...register("endDate")}
                        className={errors.endDate ? "border-red-500" : ""}
                      />
                      {errors.endDate && (
                        <p className="text-sm text-red-500 mt-1">{errors.endDate.message}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="schedule">Schedule (Placeholder)</Label>
                    <Input
                      id="schedule"
                      placeholder="Mon, Wed, Fri - 10:00 AM"
                      defaultValue="Mon, Wed, Fri - 10:00 AM"
                      {...register("schedule")}
                      className={errors.schedule ? "border-red-500" : ""}
                    />
                    {errors.schedule && (
                      <p className="text-sm text-red-500 mt-1">{errors.schedule.message}</p>
                    )}
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsCreateDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? "Creating..." : "Create Group"}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          )}
        </div>

        {/* Groups Table */}
        <Card>
          <CardHeader>
            <CardTitle>Groups</CardTitle>
            <CardDescription>
              View and manage all class groups
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex space-x-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search groups..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={courseFilter} onValueChange={setCourseFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Filter by course" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Courses</SelectItem>
                  {courses.map((course) => (
                    <SelectItem key={course.id} value={course.id}>
                      {course.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Group</TableHead>
                    <TableHead>Course</TableHead>
                    <TableHead>Teacher</TableHead>
                    <TableHead>Students</TableHead>
                    <TableHead>Schedule</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        Loading...
                      </TableCell>
                    </TableRow>
                  ) : groups.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        No groups found
                      </TableCell>
                    </TableRow>
                  ) : (
                    groups.map((group) => (
                      <TableRow key={group.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{group.name}</div>
                            <div className="text-sm text-gray-500 flex items-center">
                              <MapPin className="h-3 w-3 mr-1" />
                              {group.room || "No room"} • {group.branch}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{group.course.name}</div>
                            <div className="text-sm text-gray-500">{group.course.level}</div>
                          </div>
                        </TableCell>
                        <TableCell>{group.teacher.user.name}</TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Users className="h-4 w-4 mr-1 text-gray-400" />
                            {group._count.studentReferences}/{group.capacity}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center text-sm text-gray-500">
                            <Calendar className="h-3 w-3 mr-1" />
                            {new Date(group.startDate).toLocaleDateString()} - {new Date(group.endDate).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            {canManageGroups && (
                              <>
                                <Button variant="ghost" size="sm">
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            {pagination.pages > 1 && (
              <div className="flex items-center justify-between mt-4">
                <p className="text-sm text-gray-700">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to{" "}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
                  {pagination.total} results
                </p>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                    disabled={pagination.page === 1}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                    disabled={pagination.page === pagination.pages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
