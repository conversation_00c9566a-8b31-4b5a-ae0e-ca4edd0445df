"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Textarea } from "@/components/ui/textarea"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Plus, 
  Search, 
  Filter, 
  MessageSquare, 
  Send, 
  Edit,
  Trash2,
  Eye,
  Users,
  Megaphone,
  Clock,
  CheckCircle,
  XCircle
} from "lucide-react"
import DashboardLayout from "@/components/dashboard/layout"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"

const createMessageSchema = z.object({
  subject: z.string().min(1, "Subject is required"),
  content: z.string().min(1, "Content is required"),
  recipientType: z.enum(["ALL", "STUDENTS", "TEACHERS", "ACADEMIC_MANAGERS", "SPECIFIC"]),
  recipientIds: z.array(z.string()).optional(),
  priority: z.enum(["LOW", "MEDIUM", "HIGH"]).default("MEDIUM"),
  sendNow: z.boolean().default(false),
})

const createAnnouncementSchema = z.object({
  title: z.string().min(1, "Title is required"),
  content: z.string().min(1, "Content is required"),
  priority: z.enum(["LOW", "MEDIUM", "HIGH"]).default("MEDIUM"),
  targetAudience: z.enum(["ALL", "STUDENTS", "TEACHERS", "ACADEMIC_MANAGERS"]).default("ALL"),
  isActive: z.boolean().default(true),
})

type CreateMessageForm = z.infer<typeof createMessageSchema>
type CreateAnnouncementForm = z.infer<typeof createAnnouncementSchema>

interface Message {
  id: string
  subject: string
  content: string
  recipientType: string
  recipientIds: string[]
  priority: string
  status: string
  sentAt?: string
  createdAt: string
  sender: {
    id: string
    name: string
    role: string
  }
}

interface Announcement {
  id: string
  title: string
  content: string
  priority: string
  targetAudience: string
  isActive: boolean
  createdAt: string
  author: {
    id: string
    name: string
    role: string
  }
}

const priorityColors = {
  LOW: "bg-gray-100 text-gray-800",
  MEDIUM: "bg-blue-100 text-blue-800", 
  HIGH: "bg-red-100 text-red-800"
}

const statusColors = {
  DRAFT: "bg-yellow-100 text-yellow-800",
  SENT: "bg-green-100 text-green-800",
  FAILED: "bg-red-100 text-red-800"
}

const recipientTypeLabels = {
  ALL: "All Users",
  STUDENTS: "Students",
  TEACHERS: "Teachers",
  ACADEMIC_MANAGERS: "Academic Managers",
  SPECIFIC: "Specific Users"
}

export default function MessagesManagementPage() {
  const { data: session } = useSession()
  const [messages, setMessages] = useState<Message[]>([])
  const [announcements, setAnnouncements] = useState<Announcement[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("")
  const [priorityFilter, setPriorityFilter] = useState("")
  const [isCreateMessageDialogOpen, setIsCreateMessageDialogOpen] = useState(false)
  const [isCreateAnnouncementDialogOpen, setIsCreateAnnouncementDialogOpen] = useState(false)

  const messageForm = useForm<CreateMessageForm>({
    resolver: zodResolver(createMessageSchema),
    defaultValues: {
      priority: "MEDIUM",
      sendNow: false
    }
  })

  const announcementForm = useForm<CreateAnnouncementForm>({
    resolver: zodResolver(createAnnouncementSchema),
    defaultValues: {
      priority: "MEDIUM",
      targetAudience: "ALL",
      isActive: true
    }
  })

  useEffect(() => {
    fetchMessages()
    fetchAnnouncements()
  }, [])

  const fetchMessages = async () => {
    try {
      const response = await fetch("/api/messages")
      if (response.ok) {
        const data = await response.json()
        setMessages(data.messages || [])
      }
    } catch (error) {
      console.error("Error fetching messages:", error)
    } finally {
      setLoading(false)
    }
  }

  const fetchAnnouncements = async () => {
    try {
      const response = await fetch("/api/announcements")
      if (response.ok) {
        const data = await response.json()
        setAnnouncements(data.announcements || [])
      }
    } catch (error) {
      console.error("Error fetching announcements:", error)
    }
  }

  const onSubmitMessage = async (data: CreateMessageForm) => {
    try {
      const response = await fetch("/api/messages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        await fetchMessages()
        setIsCreateMessageDialogOpen(false)
        messageForm.reset()
      } else {
        const error = await response.json()
        console.error("Error creating message:", error)
      }
    } catch (error) {
      console.error("Error creating message:", error)
    }
  }

  const onSubmitAnnouncement = async (data: CreateAnnouncementForm) => {
    try {
      const response = await fetch("/api/announcements", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        await fetchAnnouncements()
        setIsCreateAnnouncementDialogOpen(false)
        announcementForm.reset()
      } else {
        const error = await response.json()
        console.error("Error creating announcement:", error)
      }
    } catch (error) {
      console.error("Error creating announcement:", error)
    }
  }

  const sendMessage = async (messageId: string) => {
    try {
      const response = await fetch(`/api/messages/${messageId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ action: "send" }),
      })

      if (response.ok) {
        await fetchMessages()
      } else {
        const error = await response.json()
        console.error("Error sending message:", error)
      }
    } catch (error) {
      console.error("Error sending message:", error)
    }
  }

  const filteredMessages = messages.filter(message => {
    const matchesSearch = message.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         message.content.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = !statusFilter || message.status === statusFilter
    const matchesPriority = !priorityFilter || message.priority === priorityFilter

    return matchesSearch && matchesStatus && matchesPriority
  })

  const filteredAnnouncements = announcements.filter(announcement => {
    const matchesSearch = announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         announcement.content.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesPriority = !priorityFilter || announcement.priority === priorityFilter

    return matchesSearch && matchesPriority
  })

  const canCreateMessages = session?.user?.role && ["ADMIN", "MANAGER", "TEACHER", "ACADEMIC_MANAGER"].includes(session.user.role)
  const canCreateAnnouncements = session?.user?.role && ["ADMIN", "MANAGER", "ACADEMIC_MANAGER"].includes(session.user.role)

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading messages...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Messages & Announcements</h1>
            <p className="text-gray-600">Manage communication with students and staff</p>
          </div>
          
          <div className="flex space-x-2">
            {canCreateAnnouncements && (
              <Dialog open={isCreateAnnouncementDialogOpen} onOpenChange={setIsCreateAnnouncementDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Megaphone className="h-4 w-4 mr-2" />
                    New Announcement
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>Create Announcement</DialogTitle>
                    <DialogDescription>
                      Create a new announcement for your target audience
                    </DialogDescription>
                  </DialogHeader>
                  
                  <form onSubmit={announcementForm.handleSubmit(onSubmitAnnouncement)} className="space-y-4">
                    <div>
                      <Label htmlFor="title">Title</Label>
                      <Input
                        id="title"
                        {...announcementForm.register("title")}
                        placeholder="Announcement title"
                      />
                      {announcementForm.formState.errors.title && (
                        <p className="text-sm text-red-600 mt-1">{announcementForm.formState.errors.title.message}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="content">Content</Label>
                      <Textarea
                        id="content"
                        {...announcementForm.register("content")}
                        placeholder="Announcement content"
                        rows={4}
                      />
                      {announcementForm.formState.errors.content && (
                        <p className="text-sm text-red-600 mt-1">{announcementForm.formState.errors.content.message}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="targetAudience">Target Audience</Label>
                      <Select onValueChange={(value) => announcementForm.setValue("targetAudience", value as any)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select audience" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ALL">All Users</SelectItem>
                          <SelectItem value="STUDENTS">Students</SelectItem>
                          <SelectItem value="TEACHERS">Teachers</SelectItem>
                          <SelectItem value="ACADEMIC_MANAGERS">Academic Managers</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="priority">Priority</Label>
                      <Select onValueChange={(value) => announcementForm.setValue("priority", value as any)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="LOW">Low</SelectItem>
                          <SelectItem value="MEDIUM">Medium</SelectItem>
                          <SelectItem value="HIGH">High</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex justify-end space-x-2 pt-4">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsCreateAnnouncementDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button type="submit" disabled={announcementForm.formState.isSubmitting}>
                        {announcementForm.formState.isSubmitting ? "Creating..." : "Create Announcement"}
                      </Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>
            )}
            
            {canCreateMessages && (
              <Dialog open={isCreateMessageDialogOpen} onOpenChange={setIsCreateMessageDialogOpen}>
                <DialogTrigger asChild>
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    New Message
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>Create Message</DialogTitle>
                    <DialogDescription>
                      Send a message to your selected recipients
                    </DialogDescription>
                  </DialogHeader>

                  <form onSubmit={messageForm.handleSubmit(onSubmitMessage)} className="space-y-4">
                    <div>
                      <Label htmlFor="subject">Subject</Label>
                      <Input
                        id="subject"
                        {...messageForm.register("subject")}
                        placeholder="Message subject"
                      />
                      {messageForm.formState.errors.subject && (
                        <p className="text-sm text-red-600 mt-1">{messageForm.formState.errors.subject.message}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="content">Content</Label>
                      <Textarea
                        id="content"
                        {...messageForm.register("content")}
                        placeholder="Message content"
                        rows={4}
                      />
                      {messageForm.formState.errors.content && (
                        <p className="text-sm text-red-600 mt-1">{messageForm.formState.errors.content.message}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="recipientType">Recipients</Label>
                      <Select onValueChange={(value) => messageForm.setValue("recipientType", value as any)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select recipients" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ALL">All Users</SelectItem>
                          <SelectItem value="STUDENTS">Students</SelectItem>
                          <SelectItem value="TEACHERS">Teachers</SelectItem>
                          <SelectItem value="ACADEMIC_MANAGERS">Academic Managers</SelectItem>
                          <SelectItem value="SPECIFIC">Specific Users</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="priority">Priority</Label>
                      <Select onValueChange={(value) => messageForm.setValue("priority", value as any)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="LOW">Low</SelectItem>
                          <SelectItem value="MEDIUM">Medium</SelectItem>
                          <SelectItem value="HIGH">High</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="sendNow"
                        {...messageForm.register("sendNow")}
                        className="rounded border-gray-300"
                      />
                      <Label htmlFor="sendNow">Send immediately</Label>
                    </div>

                    <div className="flex justify-end space-x-2 pt-4">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsCreateMessageDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button type="submit" disabled={messageForm.formState.isSubmitting}>
                        {messageForm.formState.isSubmitting ? "Creating..." : "Create Message"}
                      </Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>
            )}
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="search">Search</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="search"
                    placeholder="Search messages..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="status-filter">Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All statuses</SelectItem>
                    <SelectItem value="DRAFT">Draft</SelectItem>
                    <SelectItem value="SENT">Sent</SelectItem>
                    <SelectItem value="FAILED">Failed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="priority-filter">Priority</Label>
                <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All priorities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All priorities</SelectItem>
                    <SelectItem value="LOW">Low</SelectItem>
                    <SelectItem value="MEDIUM">Medium</SelectItem>
                    <SelectItem value="HIGH">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-end">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm("")
                    setStatusFilter("")
                    setPriorityFilter("")
                  }}
                  className="w-full"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Clear Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tabs for Messages and Announcements */}
        <Tabs defaultValue="messages" className="space-y-4">
          <TabsList>
            <TabsTrigger value="messages" className="flex items-center">
              <MessageSquare className="h-4 w-4 mr-2" />
              Messages ({filteredMessages.length})
            </TabsTrigger>
            <TabsTrigger value="announcements" className="flex items-center">
              <Megaphone className="h-4 w-4 mr-2" />
              Announcements ({filteredAnnouncements.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="messages">
            <Card>
              <CardHeader>
                <CardTitle>Messages</CardTitle>
                <CardDescription>
                  Manage and send messages to users
                </CardDescription>
              </CardHeader>
              <CardContent>
                {filteredMessages.length === 0 ? (
                  <div className="text-center py-8">
                    <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No messages found</h3>
                    <p className="text-gray-600 mb-4">
                      {messages.length === 0
                        ? "Get started by creating your first message."
                        : "Try adjusting your search criteria."
                      }
                    </p>
                    {canCreateMessages && messages.length === 0 && (
                      <Button onClick={() => setIsCreateMessageDialogOpen(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        Create First Message
                      </Button>
                    )}
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Subject</TableHead>
                          <TableHead>Recipients</TableHead>
                          <TableHead>Priority</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Created</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredMessages.map((message) => (
                          <TableRow key={message.id}>
                            <TableCell>
                              <div>
                                <div className="font-medium">{message.subject}</div>
                                <div className="text-sm text-gray-500 truncate max-w-xs">
                                  {message.content}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline">
                                {recipientTypeLabels[message.recipientType as keyof typeof recipientTypeLabels]}
                              </Badge>
                              {message.recipientType === "SPECIFIC" && (
                                <div className="text-xs text-gray-500 mt-1">
                                  {message.recipientIds.length} recipients
                                </div>
                              )}
                            </TableCell>
                            <TableCell>
                              <Badge className={priorityColors[message.priority as keyof typeof priorityColors]}>
                                {message.priority}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge className={statusColors[message.status as keyof typeof statusColors]}>
                                {message.status}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="text-sm text-gray-600">
                                {new Date(message.createdAt).toLocaleDateString()}
                              </div>
                              <div className="text-xs text-gray-500">
                                by {message.sender.name}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex space-x-2">
                                <Button variant="ghost" size="sm">
                                  <Eye className="h-4 w-4" />
                                </Button>
                                {message.status === "DRAFT" && (
                                  <>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => sendMessage(message.id)}
                                    >
                                      <Send className="h-4 w-4" />
                                    </Button>
                                    <Button variant="ghost" size="sm">
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                    <Button variant="ghost" size="sm">
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="announcements">
            <Card>
              <CardHeader>
                <CardTitle>Announcements</CardTitle>
                <CardDescription>
                  Manage public announcements for your audience
                </CardDescription>
              </CardHeader>
              <CardContent>
                {filteredAnnouncements.length === 0 ? (
                  <div className="text-center py-8">
                    <Megaphone className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No announcements found</h3>
                    <p className="text-gray-600 mb-4">
                      {announcements.length === 0
                        ? "Get started by creating your first announcement."
                        : "Try adjusting your search criteria."
                      }
                    </p>
                    {canCreateAnnouncements && announcements.length === 0 && (
                      <Button onClick={() => setIsCreateAnnouncementDialogOpen(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        Create First Announcement
                      </Button>
                    )}
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Title</TableHead>
                          <TableHead>Target Audience</TableHead>
                          <TableHead>Priority</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Created</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredAnnouncements.map((announcement) => (
                          <TableRow key={announcement.id}>
                            <TableCell>
                              <div>
                                <div className="font-medium">{announcement.title}</div>
                                <div className="text-sm text-gray-500 truncate max-w-xs">
                                  {announcement.content}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline">
                                {recipientTypeLabels[announcement.targetAudience as keyof typeof recipientTypeLabels] || announcement.targetAudience}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge className={priorityColors[announcement.priority as keyof typeof priorityColors]}>
                                {announcement.priority}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge variant={announcement.isActive ? "default" : "secondary"}>
                                {announcement.isActive ? "Active" : "Inactive"}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="text-sm text-gray-600">
                                {new Date(announcement.createdAt).toLocaleDateString()}
                              </div>
                              <div className="text-xs text-gray-500">
                                by {announcement.author.name}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex space-x-2">
                                <Button variant="ghost" size="sm">
                                  <Eye className="h-4 w-4" />
                                </Button>
                                {canCreateAnnouncements && (
                                  <>
                                    <Button variant="ghost" size="sm">
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                    <Button variant="ghost" size="sm">
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
