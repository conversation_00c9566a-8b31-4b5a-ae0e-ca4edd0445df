"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Eye, 
  Phone, 
  PhoneCall, 
  PhoneOff, 
  Clock, 
  User,
  Calendar,
  MessageSquare
} from "lucide-react"
import DashboardLayout from "@/components/dashboard/layout"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"

const createLeadSchema = z.object({
  name: z.string().min(1, "Name is required"),
  phone: z.string().min(1, "Phone is required"),
  coursePreference: z.string().min(1, "Course preference is required"),
  source: z.string().optional(),
  notes: z.string().optional(),
  branch: z.string().default("main"),
  followUpDate: z.string().optional(),
})

const endCallSchema = z.object({
  notes: z.string().optional(),
  outcome: z.enum(["INTERESTED", "NOT_INTERESTED", "CALLBACK", "ENROLLED"]),
})

type CreateLeadForm = z.infer<typeof createLeadSchema>
type EndCallForm = z.infer<typeof endCallSchema>

interface Lead {
  id: string
  name: string
  phone: string
  coursePreference: string
  status: string
  source?: string
  notes?: string
  branch: string
  followUpDate?: string
  callStartedAt?: string
  callEndedAt?: string
  callDuration?: number
  assignedTo?: string
  createdAt: string
  assignedGroup?: {
    name: string
    course: {
      name: string
    }
  }
  assignedTeacher?: {
    user: {
      name: string
    }
  }
  callRecords?: Array<{
    startedAt: string
    endedAt?: string
    duration?: number
    notes?: string
    user: {
      name: string
    }
  }>
}

const statusLabels = {
  NEW: "New",
  CALLING: "Calling",
  CALL_COMPLETED: "Call Completed",
  GROUP_ASSIGNED: "Group Assigned",
  ARCHIVED: "Archived",
  NOT_INTERESTED: "Not Interested"
}

const statusColors = {
  NEW: "bg-blue-100 text-blue-800",
  CALLING: "bg-yellow-100 text-yellow-800",
  CALL_COMPLETED: "bg-green-100 text-green-800",
  GROUP_ASSIGNED: "bg-purple-100 text-purple-800",
  ARCHIVED: "bg-gray-100 text-gray-800",
  NOT_INTERESTED: "bg-red-100 text-red-800"
}

export default function LeadsManagementPage() {
  const { data: session } = useSession()
  const [leads, setLeads] = useState<Lead[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isCallDialogOpen, setIsCallDialogOpen] = useState(false)
  const [currentCallLead, setCurrentCallLead] = useState<Lead | null>(null)
  const [callStartTime, setCallStartTime] = useState<Date | null>(null)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  })

  const {
    register: registerCreate,
    handleSubmit: handleSubmitCreate,
    formState: { errors: errorsCreate, isSubmitting: isSubmittingCreate },
    reset: resetCreate,
    setValue: setValueCreate,
  } = useForm<CreateLeadForm>({
    resolver: zodResolver(createLeadSchema),
  })

  const {
    register: registerCall,
    handleSubmit: handleSubmitCall,
    formState: { errors: errorsCall, isSubmitting: isSubmittingCall },
    reset: resetCall,
    setValue: setValueCall,
  } = useForm<EndCallForm>({
    resolver: zodResolver(endCallSchema),
  })

  const fetchLeads = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && { status: statusFilter })
      })

      const response = await fetch(`/api/leads?${params}`)
      if (response.ok) {
        const data = await response.json()
        setLeads(data.leads)
        setPagination(data.pagination)
      }
    } catch (error) {
      console.error("Error fetching leads:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchLeads()
  }, [pagination.page, searchTerm, statusFilter])

  const onSubmitCreate = async (data: CreateLeadForm) => {
    try {
      const response = await fetch("/api/leads", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        setIsCreateDialogOpen(false)
        resetCreate()
        fetchLeads()
      } else {
        const error = await response.json()
        console.error("Error creating lead:", error)
      }
    } catch (error) {
      console.error("Error creating lead:", error)
    }
  }

  const startCall = async (lead: Lead) => {
    try {
      const response = await fetch(`/api/leads/${lead.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ action: "start_call" }),
      })

      if (response.ok) {
        setCurrentCallLead(lead)
        setCallStartTime(new Date())
        setIsCallDialogOpen(true)
        fetchLeads()
      }
    } catch (error) {
      console.error("Error starting call:", error)
    }
  }

  const onSubmitEndCall = async (data: EndCallForm) => {
    if (!currentCallLead) return

    try {
      const response = await fetch(`/api/leads/${currentCallLead.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ 
          action: "end_call",
          ...data 
        }),
      })

      if (response.ok) {
        setIsCallDialogOpen(false)
        setCurrentCallLead(null)
        setCallStartTime(null)
        resetCall()
        fetchLeads()
      }
    } catch (error) {
      console.error("Error ending call:", error)
    }
  }

  const handleDelete = async (leadId: string) => {
    if (!confirm("Are you sure you want to delete this lead?")) return

    try {
      const response = await fetch(`/api/leads/${leadId}`, {
        method: "DELETE",
      })

      if (response.ok) {
        fetchLeads()
      } else {
        const error = await response.json()
        console.error("Error deleting lead:", error)
      }
    } catch (error) {
      console.error("Error deleting lead:", error)
    }
  }

  const formatCallDuration = (seconds?: number) => {
    if (!seconds) return "0s"
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}m ${secs}s`
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Lead Management</h1>
            <p className="text-gray-600">Track and manage potential students</p>
          </div>
          
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Lead
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Add New Lead</DialogTitle>
                <DialogDescription>
                  Register a new potential student lead.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmitCreate(onSubmitCreate)} className="space-y-4">
                <div>
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    {...registerCreate("name")}
                    className={errorsCreate.name ? "border-red-500" : ""}
                  />
                  {errorsCreate.name && (
                    <p className="text-sm text-red-500 mt-1">{errorsCreate.name.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="+998901234567"
                    {...registerCreate("phone")}
                    className={errorsCreate.phone ? "border-red-500" : ""}
                  />
                  {errorsCreate.phone && (
                    <p className="text-sm text-red-500 mt-1">{errorsCreate.phone.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="coursePreference">Course Interest</Label>
                  <Input
                    id="coursePreference"
                    placeholder="e.g., IELTS, General English"
                    {...registerCreate("coursePreference")}
                    className={errorsCreate.coursePreference ? "border-red-500" : ""}
                  />
                  {errorsCreate.coursePreference && (
                    <p className="text-sm text-red-500 mt-1">{errorsCreate.coursePreference.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="source">Source (Optional)</Label>
                  <Input
                    id="source"
                    placeholder="e.g., Instagram, Referral, Walk-in"
                    {...registerCreate("source")}
                  />
                </div>

                <div>
                  <Label htmlFor="notes">Notes (Optional)</Label>
                  <Input
                    id="notes"
                    placeholder="Additional information..."
                    {...registerCreate("notes")}
                  />
                </div>

                <div>
                  <Label htmlFor="followUpDate">Follow-up Date (Optional)</Label>
                  <Input
                    id="followUpDate"
                    type="date"
                    {...registerCreate("followUpDate")}
                  />
                </div>

                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsCreateDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmittingCreate}>
                    {isSubmittingCreate ? "Creating..." : "Create Lead"}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {/* Call Dialog */}
        <Dialog open={isCallDialogOpen} onOpenChange={setIsCallDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle className="flex items-center">
                <PhoneCall className="h-5 w-5 mr-2 text-green-600" />
                Calling {currentCallLead?.name}
              </DialogTitle>
              <DialogDescription>
                {currentCallLead?.phone} • {currentCallLead?.coursePreference}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmitCall(onSubmitEndCall)} className="space-y-4">
              <div className="text-center py-4">
                <div className="text-2xl font-bold text-green-600">
                  {callStartTime && (
                    <Clock className="h-6 w-6 inline mr-2" />
                  )}
                  Call in progress...
                </div>
                <p className="text-sm text-gray-500 mt-2">
                  Started at {callStartTime?.toLocaleTimeString()}
                </p>
              </div>

              <div>
                <Label htmlFor="outcome">Call Outcome</Label>
                <Select onValueChange={(value) => setValueCall("outcome", value as any)}>
                  <SelectTrigger className={errorsCall.outcome ? "border-red-500" : ""}>
                    <SelectValue placeholder="Select outcome" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="INTERESTED">Interested</SelectItem>
                    <SelectItem value="NOT_INTERESTED">Not Interested</SelectItem>
                    <SelectItem value="CALLBACK">Needs Callback</SelectItem>
                    <SelectItem value="ENROLLED">Enrolled</SelectItem>
                  </SelectContent>
                </Select>
                {errorsCall.outcome && (
                  <p className="text-sm text-red-500 mt-1">{errorsCall.outcome.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="callNotes">Call Notes</Label>
                <Input
                  id="callNotes"
                  placeholder="Notes about the conversation..."
                  {...registerCall("notes")}
                />
              </div>

              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setIsCallDialogOpen(false)
                    setCurrentCallLead(null)
                    setCallStartTime(null)
                  }}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmittingCall} className="bg-red-600 hover:bg-red-700">
                  <PhoneOff className="h-4 w-4 mr-2" />
                  {isSubmittingCall ? "Ending..." : "End Call"}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>

        {/* Leads Table */}
        <Card>
          <CardHeader>
            <CardTitle>Leads</CardTitle>
            <CardDescription>
              View and manage all potential students
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex space-x-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search leads..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Status</SelectItem>
                  {Object.entries(statusLabels).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Lead</TableHead>
                    <TableHead>Course Interest</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Contact</TableHead>
                    <TableHead>Source</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        Loading...
                      </TableCell>
                    </TableRow>
                  ) : leads.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        No leads found
                      </TableCell>
                    </TableRow>
                  ) : (
                    leads.map((lead) => (
                      <TableRow key={lead.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{lead.name}</div>
                            <div className="text-sm text-gray-500 flex items-center">
                              <Phone className="h-3 w-3 mr-1" />
                              {lead.phone}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{lead.coursePreference}</TableCell>
                        <TableCell>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[lead.status as keyof typeof statusColors]}`}>
                            {statusLabels[lead.status as keyof typeof statusLabels]}
                          </span>
                        </TableCell>
                        <TableCell>
                          {lead.callRecords && lead.callRecords.length > 0 ? (
                            <div className="text-sm">
                              <div>{new Date(lead.callRecords[0].startedAt).toLocaleDateString()}</div>
                              {lead.callRecords[0].duration && (
                                <div className="text-gray-500">
                                  {formatCallDuration(lead.callRecords[0].duration)}
                                </div>
                              )}
                            </div>
                          ) : (
                            <span className="text-gray-400">Never contacted</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-gray-500">{lead.source || "Unknown"}</span>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            {lead.status !== "CALLING" && (
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => startCall(lead)}
                                className="text-green-600 hover:text-green-700"
                              >
                                <PhoneCall className="h-4 w-4" />
                              </Button>
                            )}
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            {session?.user?.role && ["ADMIN", "MANAGER"].includes(session.user.role) && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDelete(lead.id)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            {pagination.pages > 1 && (
              <div className="flex items-center justify-between mt-4">
                <p className="text-sm text-gray-700">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to{" "}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
                  {pagination.total} results
                </p>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                    disabled={pagination.page === 1}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                    disabled={pagination.page === pagination.pages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
