import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const updatePaymentSchema = z.object({
  amount: z.number().min(0, "Amount must be positive").optional(),
  method: z.enum(["CASH", "CARD"]).optional(),
  status: z.enum(["PAID", "DEBT", "REFUNDED"]).optional(),
  description: z.string().optional(),
  transactionId: z.string().optional(),
  dueDate: z.string().optional().transform((str) => str ? new Date(str) : undefined),
  paidDate: z.string().optional().transform((str) => str ? new Date(str) : undefined),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const payment = await prisma.paymentOverview.findUnique({
      where: { id: params.id },
      include: {
        studentReference: {
          include: {
            currentGroup: {
              include: {
                course: true,
                teacher: {
                  include: {
                    user: true
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!payment) {
      return NextResponse.json({ error: "Payment not found" }, { status: 404 })
    }

    return NextResponse.json(payment)
  } catch (error) {
    console.error("Error fetching payment:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "CASHIER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updatePaymentSchema.parse(body)

    // Check if payment exists
    const existingPayment = await prisma.paymentOverview.findUnique({
      where: { id: params.id },
      include: {
        studentReference: true
      }
    })

    if (!existingPayment) {
      return NextResponse.json({ error: "Payment not found" }, { status: 404 })
    }

    // Update payment
    const payment = await prisma.paymentOverview.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        studentReference: {
          include: {
            currentGroup: {
              include: {
                course: true,
                teacher: {
                  include: {
                    user: true
                  }
                }
              }
            }
          }
        }
      }
    })

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: session.user.id,
        userRole: session.user.role as any,
        action: "UPDATE",
        resource: "payment",
        resourceId: payment.id,
        details: {
          changes: validatedData,
          studentName: existingPayment.studentReference.name
        }
      }
    })

    return NextResponse.json(payment)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error updating payment:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check if payment exists
    const existingPayment = await prisma.paymentOverview.findUnique({
      where: { id: params.id },
      include: {
        studentReference: true
      }
    })

    if (!existingPayment) {
      return NextResponse.json({ error: "Payment not found" }, { status: 404 })
    }

    // Delete payment
    await prisma.paymentOverview.delete({
      where: { id: params.id }
    })

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: session.user.id,
        userRole: session.user.role as any,
        action: "DELETE",
        resource: "payment",
        resourceId: params.id,
        details: {
          amount: existingPayment.amount,
          studentName: existingPayment.studentReference.name
        }
      }
    })

    return NextResponse.json({ message: "Payment deleted successfully" })
  } catch (error) {
    console.error("Error deleting payment:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
