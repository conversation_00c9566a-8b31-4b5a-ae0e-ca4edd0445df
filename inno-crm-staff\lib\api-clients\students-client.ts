import crypto from 'crypto'

interface ApiClientConfig {
  baseUrl: string
  apiKey: string
  secretKey: string
}

interface CreateStudentData {
  name: string
  phone: string
  email?: string
  level: string
  branch: string
  emergencyContact?: string
  dateOfBirth?: string
  address?: string
}

interface UpdateStudentData {
  name?: string
  phone?: string
  email?: string
  level?: string
  status?: string
  emergencyContact?: string
  currentGroupId?: string
}

interface Student {
  id: string
  name: string
  phone: string
  email?: string
  level: string
  status: string
  branch: string
  emergencyContact?: string
  currentGroupId?: string
  createdAt: string
  updatedAt: string
}

interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export class StudentsApiClient {
  private config: ApiClientConfig

  constructor(config: ApiClientConfig) {
    this.config = config
  }

  private generateSignature(method: string, path: string, body: string, timestamp: string): string {
    const message = `${method}|${path}|${body}|${timestamp}`
    return crypto
      .createHmac('sha256', this.config.secretKey)
      .update(message)
      .digest('hex')
  }

  private async makeRequest<T>(
    method: string,
    path: string,
    data?: any
  ): Promise<ApiResponse<T>> {
    const timestamp = new Date().toISOString()
    const body = data ? JSON.stringify(data) : ''
    const signature = this.generateSignature(method, path, body, timestamp)

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'X-API-Key': this.config.apiKey,
      'X-Server-Source': 'STAFF',
      'X-Signature': signature,
      'X-Timestamp': timestamp,
    }

    try {
      const response = await fetch(`${this.config.baseUrl}${path}`, {
        method,
        headers,
        body: data ? JSON.stringify(data) : undefined,
      })

      const result = await response.json()

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP ${response.status}: ${response.statusText}`,
        }
      }

      return {
        success: true,
        data: result,
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      }
    }
  }

  async createStudent(data: CreateStudentData): Promise<ApiResponse<Student>> {
    return this.makeRequest<Student>('POST', '/api/inter-server/students', data)
  }

  async updateStudent(id: string, data: UpdateStudentData): Promise<ApiResponse<Student>> {
    return this.makeRequest<Student>('PUT', `/api/inter-server/students/${id}`, data)
  }

  async getStudent(id: string): Promise<ApiResponse<Student>> {
    return this.makeRequest<Student>('GET', `/api/inter-server/students/${id}`)
  }

  async deleteStudent(id: string): Promise<ApiResponse<void>> {
    return this.makeRequest<void>('DELETE', `/api/inter-server/students/${id}`)
  }

  async assignStudentToGroup(studentId: string, groupId: string): Promise<ApiResponse<void>> {
    return this.makeRequest<void>('POST', `/api/inter-server/students/${studentId}/assign-group`, {
      groupId,
    })
  }

  async recordPayment(data: {
    studentId: string
    amount: number
    method: 'CASH' | 'CARD'
    description?: string
    dueDate?: string
  }): Promise<ApiResponse<any>> {
    return this.makeRequest('POST', '/api/inter-server/payments', data)
  }

  async getStudentPayments(studentId: string): Promise<ApiResponse<any[]>> {
    return this.makeRequest<any[]>('GET', `/api/inter-server/students/${studentId}/payments`)
  }

  async markAttendance(data: {
    studentId: string
    classReferenceId: string
    groupReferenceId: string
    status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EXCUSED'
    notes?: string
  }): Promise<ApiResponse<any>> {
    return this.makeRequest('POST', '/api/inter-server/attendance', data)
  }

  async getStudentAttendance(studentId: string): Promise<ApiResponse<any[]>> {
    return this.makeRequest<any[]>('GET', `/api/inter-server/students/${studentId}/attendance`)
  }

  async sendMessage(data: {
    subject: string
    content: string
    recipientType: 'ALL' | 'STUDENTS' | 'SPECIFIC'
    recipientIds?: string[]
    priority?: 'LOW' | 'MEDIUM' | 'HIGH'
  }): Promise<ApiResponse<any>> {
    return this.makeRequest('POST', '/api/inter-server/messages', data)
  }

  async syncGroupReference(groupData: {
    id: string
    name: string
    teacherReferenceId: string
    courseName: string
    schedule: any
    room?: string
    branch: string
    startDate: string
    endDate: string
    isActive: boolean
  }): Promise<ApiResponse<void>> {
    return this.makeRequest<void>('POST', '/api/inter-server/sync/groups', groupData)
  }

  async syncTeacherReference(teacherData: {
    id: string
    name: string
    subject: string
    branch: string
    photoUrl?: string
  }): Promise<ApiResponse<void>> {
    return this.makeRequest<void>('POST', '/api/inter-server/sync/teachers', teacherData)
  }

  async healthCheck(): Promise<ApiResponse<{ status: string; timestamp: string }>> {
    return this.makeRequest<{ status: string; timestamp: string }>('GET', '/api/health')
  }
}

// Create singleton instance
let studentsClient: StudentsApiClient | null = null

export function getStudentsClient(): StudentsApiClient {
  if (!studentsClient) {
    studentsClient = new StudentsApiClient({
      baseUrl: process.env.STUDENTS_SERVER_URL || 'https://students.innovative-centre.uz',
      apiKey: process.env.STUDENTS_API_KEY || '',
      secretKey: process.env.STUDENTS_SECRET_KEY || '',
    })
  }
  return studentsClient
}
