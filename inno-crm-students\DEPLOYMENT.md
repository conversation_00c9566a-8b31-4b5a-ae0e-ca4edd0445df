# Student Portal Deployment Guide

## Overview
This guide provides step-by-step instructions for deploying the Innovative Centre Student Portal to Vercel with public access and PWA capabilities.

## Prerequisites
- Vercel account (Free tier sufficient)
- Neon PostgreSQL database
- Custom domain: `students.innovative-centre.uz`

## Environment Variables

### Required Environment Variables
Copy `.env.example` to `.env.local` and configure the following:

```env
# Database
DATABASE_URL="**************************************************************************"

# Authentication
NEXTAUTH_SECRET="your-32-character-secret-key"
NEXTAUTH_URL="https://students.innovative-centre.uz"

# Inter-server Communication
STAFF_SERVER_URL="https://staff.innovative-centre.uz"
STAFF_API_KEY="secure-api-key"
STAFF_SECRET_KEY="secure-secret-key"

# Application Configuration
APP_NAME="Innovative Centre - Student Portal"
ENABLE_PWA="true"
```

## Vercel Deployment Steps

### 1. Connect Repository
```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Link project
vercel link
```

### 2. Configure Environment Variables
```bash
# Set production environment variables
vercel env add DATABASE_URL production
vercel env add NEXTAUTH_SECRET production
vercel env add NEXTAUTH_URL production
vercel env add STAFF_SERVER_URL production
vercel env add STAFF_API_KEY production
vercel env add STAFF_SECRET_KEY production
vercel env add APP_NAME production
vercel env add ENABLE_PWA production
```

### 3. Configure Custom Domain
1. Go to Vercel Dashboard > Project Settings > Domains
2. Add custom domain: `students.innovative-centre.uz`
3. Configure DNS records as instructed by Vercel

### 4. Deploy
```bash
# Deploy to production
vercel --prod
```

## Database Setup

### 1. Run Migrations
```bash
# Generate Prisma client
npx prisma generate

# Push schema to database
npx prisma db push
```

### 2. Seed Initial Data (Optional)
```bash
# Create sample data
npx prisma db seed
```

## PWA Configuration

### Features Enabled
- Service Worker for offline functionality
- Web App Manifest for installability
- Push notifications (ready for implementation)
- Offline page with cached content
- Mobile-optimized interface

### PWA Validation
Test PWA features using:
```bash
# Run Lighthouse audit
npx lighthouse https://students.innovative-centre.uz --view
```

### Installation
Students can install the app by:
1. Visiting the site in Chrome/Edge
2. Clicking "Install" when prompted
3. Or using "Add to Home Screen" in mobile browsers

## Security Features

### 1. Headers Configuration
- X-Frame-Options: SAMEORIGIN
- X-Content-Type-Options: nosniff
- X-XSS-Protection: 1; mode=block
- Referrer-Policy: strict-origin-when-cross-origin

### 2. Authentication
- NextAuth.js with secure session management
- Student-only access control
- Session timeout and refresh

### 3. API Security
- Input validation and sanitization
- Rate limiting for student endpoints
- Secure inter-server communication

## Mobile Optimization

### Responsive Design
- Mobile-first approach
- Touch-friendly interface
- Optimized for various screen sizes

### Performance
- Optimized images and assets
- Code splitting for faster loading
- Service Worker caching

## Monitoring & Health Checks

### Health Check Endpoint
```
GET /api/health
```

Response:
```json
{
  "status": "healthy",
  "checks": {
    "database": { "status": "ok" },
    "interServerComm": { "status": "ok" }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Analytics
- Vercel Analytics enabled
- PWA usage tracking
- Performance monitoring

## Troubleshooting

### Common Issues

#### 1. PWA Not Installing
- Verify manifest.json is accessible
- Check service worker registration
- Ensure HTTPS is enabled
- Validate PWA criteria with Lighthouse

#### 2. Database Connection Issues
- Verify DATABASE_URL is correct
- Check Neon database is accessible
- Ensure SSL mode is enabled

#### 3. Mobile Performance Issues
- Check image optimization
- Verify service worker caching
- Test on actual devices

### Debug Commands
```bash
# Check environment variables
vercel env ls

# View deployment logs
vercel logs

# Test PWA features
npx lighthouse https://students.innovative-centre.uz --only-categories=pwa
```

## Rollback Procedures

### Quick Rollback
```bash
# Rollback to previous deployment
vercel rollback
```

### Manual Rollback
1. Go to Vercel Dashboard > Deployments
2. Find previous successful deployment
3. Click "Promote to Production"

## Maintenance

### Regular Tasks
- Monitor student usage patterns
- Review performance metrics
- Update PWA features
- Backup database regularly

### Updates
```bash
# Update dependencies
npm update

# Test PWA features
npm run build && npm run start

# Deploy updates
vercel --prod
```

## Student Support

### Common Student Issues
1. **Can't install app**: Guide them through browser-specific installation
2. **Offline issues**: Explain cached content limitations
3. **Login problems**: Check credentials and session status
4. **Performance issues**: Suggest clearing cache or reinstalling

### Support Resources
- In-app help section
- Installation guides for different devices
- FAQ for common issues
- Contact information for technical support

## Performance Targets

### Metrics to Monitor
- Page load time: < 2 seconds
- PWA install rate: > 30%
- Mobile performance score: > 90
- Offline functionality: 100% for cached content

### Optimization
- Regular Lighthouse audits
- Image optimization
- Code splitting optimization
- Service Worker cache strategy tuning
