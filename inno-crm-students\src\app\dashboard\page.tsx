"use client"

import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  BookOpen, 
  Calendar, 
  CreditCard, 
  TrendingUp, 
  Clock,
  CheckCircle,
  AlertCircle,
  MessageSquare
} from "lucide-react"
import Link from "next/link"
import DashboardLayout from "@/components/dashboard/layout"

// Mock data - in real app, this would come from API
const studentStats = [
  {
    title: "Current Level",
    value: "B2",
    description: "Intermediate",
    icon: TrendingUp,
    color: "text-green-600"
  },
  {
    title: "Attendance Rate",
    value: "92%",
    description: "This month",
    icon: CheckCircle,
    color: "text-blue-600"
  },
  {
    title: "Next Payment",
    value: "$150",
    description: "Due in 5 days",
    icon: CreditCard,
    color: "text-orange-600"
  },
  {
    title: "Classes This Week",
    value: "6",
    description: "2 remaining",
    icon: Calendar,
    color: "text-purple-600"
  },
]

const upcomingClasses = [
  {
    id: 1,
    subject: "IELTS Speaking",
    teacher: "Ms. <PERSON>",
    time: "Today, 2:00 PM",
    room: "Room 101",
    status: "upcoming"
  },
  {
    id: 2,
    subject: "IELTS Writing",
    teacher: "Mr. <PERSON>",
    time: "Tomorrow, 10:00 AM",
    room: "Room 203",
    status: "upcoming"
  },
  {
    id: 3,
    subject: "IELTS Reading",
    teacher: "Ms. Emily",
    time: "Wed, 3:00 PM",
    room: "Room 105",
    status: "scheduled"
  },
]

const recentAnnouncements = [
  {
    id: 1,
    title: "New Study Materials Available",
    message: "IELTS practice tests have been uploaded to your course materials.",
    time: "2 hours ago",
    priority: "normal"
  },
  {
    id: 2,
    title: "Schedule Change",
    message: "Tomorrow's writing class has been moved to 11:00 AM.",
    time: "1 day ago",
    priority: "high"
  },
  {
    id: 3,
    title: "Mock Test Results",
    message: "Your latest mock test results are now available.",
    time: "2 days ago",
    priority: "normal"
  },
]

const quickActions = [
  {
    title: "View Schedule",
    description: "Check your class timetable",
    href: "/dashboard/schedule",
    icon: Calendar,
    color: "bg-blue-500"
  },
  {
    title: "Course Materials",
    description: "Access study resources",
    href: "/dashboard/courses",
    icon: BookOpen,
    color: "bg-green-500"
  },
  {
    title: "Payment History",
    description: "View payment records",
    href: "/dashboard/payments",
    icon: CreditCard,
    color: "bg-purple-500"
  },
  {
    title: "Messages",
    description: "Check notifications",
    href: "/dashboard/messages",
    icon: MessageSquare,
    color: "bg-orange-500"
  },
]

export default function StudentDashboardPage() {
  const { data: session } = useSession()

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-blue-900">Welcome Back!</h1>
          <p className="text-blue-600">
            Hi {session?.user?.name}, here's your learning progress today.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {studentStats.map((stat) => (
            <Card key={stat.title} className="border-blue-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">
                  {stat.title}
                </CardTitle>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-900">{stat.value}</div>
                <p className="text-xs text-blue-600">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Upcoming Classes */}
          <Card className="border-blue-200">
            <CardHeader>
              <CardTitle className="text-blue-900">Upcoming Classes</CardTitle>
              <CardDescription>
                Your next scheduled lessons
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingClasses.map((class_) => (
                  <div key={class_.id} className="flex items-center space-x-4 p-3 bg-blue-50 rounded-lg">
                    <div className="flex-shrink-0">
                      <Clock className="h-5 w-5 text-blue-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-blue-900">{class_.subject}</p>
                      <p className="text-xs text-blue-600">{class_.teacher} • {class_.room}</p>
                      <p className="text-xs text-gray-500">{class_.time}</p>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4">
                <Button variant="outline" size="sm" asChild className="border-blue-300 text-blue-700 hover:bg-blue-50">
                  <Link href="/dashboard/schedule">View full schedule</Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="border-blue-200">
            <CardHeader>
              <CardTitle className="text-blue-900">Quick Actions</CardTitle>
              <CardDescription>
                Common tasks and resources
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                {quickActions.map((action) => (
                  <Link key={action.title} href={action.href}>
                    <div className="flex flex-col items-center p-4 text-center border border-blue-200 rounded-lg hover:bg-blue-50 transition-colors">
                      <div className={`p-2 rounded-full ${action.color} mb-2`}>
                        <action.icon className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="font-medium text-sm text-blue-900">{action.title}</h3>
                      <p className="text-xs text-blue-600 mt-1">{action.description}</p>
                    </div>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Announcements */}
        <Card className="border-blue-200">
          <CardHeader>
            <CardTitle className="text-blue-900">Recent Announcements</CardTitle>
            <CardDescription>
              Important updates from your teachers
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentAnnouncements.map((announcement) => (
                <div key={announcement.id} className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                  <div className="flex-shrink-0">
                    {announcement.priority === 'high' ? (
                      <AlertCircle className="h-5 w-5 text-orange-500" />
                    ) : (
                      <MessageSquare className="h-5 w-5 text-blue-500" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-blue-900">{announcement.title}</p>
                    <p className="text-sm text-gray-700 mt-1">{announcement.message}</p>
                    <p className="text-xs text-blue-600 mt-2">{announcement.time}</p>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4">
              <Button variant="outline" size="sm" asChild className="border-blue-300 text-blue-700 hover:bg-blue-50">
                <Link href="/dashboard/messages">View all messages</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
