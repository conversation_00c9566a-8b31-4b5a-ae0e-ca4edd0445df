import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const syncRequestSchema = z.object({
  apiKey: z.string().min(1, "API key is required"),
  lastSyncTime: z.string().optional().transform((str) => str ? new Date(str) : undefined),
  studentIds: z.array(z.string()).optional(),
})

// Verify API key for inter-server communication
const verifyApiKey = (apiKey: string) => {
  const validApiKey = process.env.INTER_SERVER_API_KEY
  return validApiKey && apiKey === validApiKey
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { apiKey, lastSyncTime, studentIds } = syncRequestSchema.parse(body)

    // Verify API key
    if (!verifyApiKey(apiKey)) {
      return NextResponse.json({ error: "Invalid API key" }, { status: 401 })
    }

    let whereClause: any = {}

    // If specific student IDs are requested
    if (studentIds && studentIds.length > 0) {
      whereClause.id = { in: studentIds }
    }
    // If syncing based on last sync time
    else if (lastSyncTime) {
      whereClause.OR = [
        { updatedAt: { gt: lastSyncTime } },
        { createdAt: { gt: lastSyncTime } }
      ]
    }

    // Get students with their related data
    const students = await prisma.studentReference.findMany({
      where: whereClause,
      include: {
        currentGroup: {
          include: {
            course: {
              select: {
                id: true,
                name: true,
                level: true,
                description: true,
                duration: true,
                price: true
              }
            },
            teacher: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    phone: true,
                    email: true
                  }
                }
              }
            }
          }
        },
        paymentOverview: {
          orderBy: { createdAt: "desc" },
          take: 10
        }
      },
      orderBy: { updatedAt: "desc" }
    })

    // Transform data for student server
    const syncData = students.map(student => ({
      id: student.id,
      name: student.name,
      phone: student.phone,
      level: student.level,
      branch: student.branch,
      status: student.status,
      emergencyContact: student.emergencyContact,
      createdAt: student.createdAt,
      updatedAt: student.updatedAt,
      lastSyncedAt: student.lastSyncedAt,
      currentGroup: student.currentGroup ? {
        id: student.currentGroup.id,
        name: student.currentGroup.name,
        capacity: student.currentGroup.capacity,
        schedule: student.currentGroup.schedule,
        room: student.currentGroup.room,
        branch: student.currentGroup.branch,
        startDate: student.currentGroup.startDate,
        endDate: student.currentGroup.endDate,
        isActive: student.currentGroup.isActive,
        course: student.currentGroup.course,
        teacher: student.currentGroup.teacher ? {
          id: student.currentGroup.teacher.id,
          subject: student.currentGroup.teacher.subject,
          tier: student.currentGroup.teacher.tier,
          user: student.currentGroup.teacher.user
        } : null
      } : null,
      payments: student.paymentOverview.map(payment => ({
        id: payment.id,
        amount: payment.amount,
        method: payment.method,
        status: payment.status,
        description: payment.description,
        transactionId: payment.transactionId,
        dueDate: payment.dueDate,
        paidDate: payment.paidDate,
        createdAt: payment.createdAt
      }))
    }))

    // Update last synced time for these students
    if (students.length > 0) {
      await prisma.studentReference.updateMany({
        where: {
          id: { in: students.map(s => s.id) }
        },
        data: {
          lastSyncedAt: new Date()
        }
      })
    }

    return NextResponse.json({
      success: true,
      data: syncData,
      syncTime: new Date().toISOString(),
      count: syncData.length
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error syncing students:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// Get sync status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const apiKey = searchParams.get("apiKey")

    if (!apiKey || !verifyApiKey(apiKey)) {
      return NextResponse.json({ error: "Invalid API key" }, { status: 401 })
    }

    // Get sync statistics
    const totalStudents = await prisma.studentReference.count()
    const lastSyncedStudents = await prisma.studentReference.count({
      where: {
        lastSyncedAt: {
          not: null
        }
      }
    })

    const oldestUnsyncedStudent = await prisma.studentReference.findFirst({
      where: {
        OR: [
          { lastSyncedAt: null },
          { updatedAt: { gt: prisma.studentReference.fields.lastSyncedAt } }
        ]
      },
      orderBy: { updatedAt: "asc" }
    })

    const newestUpdate = await prisma.studentReference.findFirst({
      orderBy: { updatedAt: "desc" }
    })

    return NextResponse.json({
      totalStudents,
      syncedStudents: lastSyncedStudents,
      unsyncedStudents: totalStudents - lastSyncedStudents,
      oldestUnsyncedUpdate: oldestUnsyncedStudent?.updatedAt,
      newestUpdate: newestUpdate?.updatedAt,
      serverTime: new Date().toISOString()
    })
  } catch (error) {
    console.error("Error getting sync status:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
