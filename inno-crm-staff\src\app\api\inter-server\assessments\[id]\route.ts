import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { z } from "zod"

const STUDENTS_SERVER_URL = process.env.STUDENTS_SERVER_URL || "http://localhost:3001"
const INTER_SERVER_SECRET = process.env.INTER_SERVER_SECRET || "your-secret-key"

const gradeAssessmentSchema = z.object({
  score: z.number().min(0, "Score must be non-negative"),
  passed: z.boolean().optional(),
  results: z.record(z.any()).optional(),
  feedback: z.string().optional(),
  gradedBy: z.string().optional(),
  gradedAt: z.string().optional(),
})

// Helper function to make authenticated requests to students server
async function makeStudentsServerRequest(
  endpoint: string,
  options: RequestInit = {}
) {
  const url = `${STUDENTS_SERVER_URL}/api/inter-server${endpoint}`
  
  const response = await fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      'Authorization': `Bearer ${INTER_SERVER_SECRET}`,
      'Content-Type': 'application/json',
    },
  })

  if (!response.ok) {
    throw new Error(`Students server request failed: ${response.status}`)
  }

  return response.json()
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const assessment = await makeStudentsServerRequest(`/assessments/${params.id}`)

    return NextResponse.json(assessment)
  } catch (error) {
    console.error("Error fetching assessment from students server:", error)
    return NextResponse.json({ error: "Failed to fetch assessment" }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "TEACHER", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const action = body.action

    if (action === "grade") {
      const validatedData = gradeAssessmentSchema.parse(body)
      
      // Add grading information
      const gradeData = {
        ...validatedData,
        gradedBy: session.user.id,
        gradedAt: new Date().toISOString()
      }

      const assessment = await makeStudentsServerRequest(`/assessments/${params.id}`, {
        method: "PUT",
        body: JSON.stringify({
          action: "grade",
          ...gradeData
        })
      })

      return NextResponse.json(assessment)
    } else {
      // Forward other update requests
      const assessment = await makeStudentsServerRequest(`/assessments/${params.id}`, {
        method: "PUT",
        body: JSON.stringify(body)
      })

      return NextResponse.json(assessment)
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error updating assessment:", error)
    return NextResponse.json({ error: "Failed to update assessment" }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    await makeStudentsServerRequest(`/assessments/${params.id}`, {
      method: "DELETE"
    })

    return NextResponse.json({ message: "Assessment deleted successfully" })
  } catch (error) {
    console.error("Error deleting assessment:", error)
    return NextResponse.json({ error: "Failed to delete assessment" }, { status: 500 })
  }
}
