import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { z } from "zod"

const STUDENTS_SERVER_URL = process.env.STUDENTS_SERVER_URL || "http://localhost:3001"
const INTER_SERVER_SECRET = process.env.INTER_SERVER_SECRET || "your-secret-key"

const broadcastMessageSchema = z.object({
  messageId: z.string().min(1, "Message ID is required"),
  subject: z.string().min(1, "Subject is required"),
  content: z.string().min(1, "Content is required"),
  recipientType: z.enum(["ALL", "STUDENTS", "SPECIFIC"]),
  recipientIds: z.array(z.string()).optional(),
  priority: z.enum(["LOW", "MEDIUM", "HIGH"]).default("MEDIUM"),
  senderId: z.string().min(1, "Sender ID is required"),
  senderName: z.string().min(1, "Sender name is required"),
})

// Helper function to make authenticated requests to students server
async function makeStudentsServerRequest(
  endpoint: string,
  options: RequestInit = {}
) {
  const url = `${STUDENTS_SERVER_URL}/api/inter-server${endpoint}`
  
  const response = await fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      'Authorization': `Bearer ${INTER_SERVER_SECRET}`,
      'Content-Type': 'application/json',
    },
  })

  if (!response.ok) {
    throw new Error(`Students server request failed: ${response.status}`)
  }

  return response.json()
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "TEACHER", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = broadcastMessageSchema.parse(body)

    // Only broadcast if recipients include students
    if (validatedData.recipientType === "STUDENTS" || validatedData.recipientType === "ALL") {
      const messageData = {
        id: validatedData.messageId,
        subject: validatedData.subject,
        content: validatedData.content,
        recipientType: validatedData.recipientType,
        recipientIds: validatedData.recipientIds || [],
        priority: validatedData.priority,
        senderReferenceId: validatedData.senderId,
        senderName: validatedData.senderName,
        sentAt: new Date().toISOString()
      }

      const result = await makeStudentsServerRequest("/messages/broadcast", {
        method: "POST",
        body: JSON.stringify(messageData)
      })

      return NextResponse.json(result)
    } else {
      return NextResponse.json({ message: "No students to broadcast to" })
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error broadcasting message:", error)
    return NextResponse.json({ error: "Failed to broadcast message" }, { status: 500 })
  }
}
