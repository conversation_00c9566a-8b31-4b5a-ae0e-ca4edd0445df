import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get("period") || "30" // days
    const branch = searchParams.get("branch") || ""

    const startDate = new Date()
    startDate.setDate(startDate.getDate() - parseInt(period))

    const whereClause = {
      createdAt: { gte: startDate },
      ...(branch && { branch })
    }

    // Student Analytics
    const totalStudents = await prisma.studentReference.count({
      where: branch ? { branch } : {}
    })

    const activeStudents = await prisma.studentReference.count({
      where: {
        status: "ACTIVE",
        ...(branch && { branch })
      }
    })

    const newStudentsThisPeriod = await prisma.studentReference.count({
      where: whereClause
    })

    const studentsByStatus = await prisma.studentReference.groupBy({
      by: ['status'],
      where: branch ? { branch } : {},
      _count: { _all: true }
    })

    const studentsByLevel = await prisma.studentReference.groupBy({
      by: ['level'],
      where: branch ? { branch } : {},
      _count: { _all: true }
    })

    // Lead Analytics
    const totalLeads = await prisma.lead.count({
      where: branch ? { branch } : {}
    })

    const newLeadsThisPeriod = await prisma.lead.count({
      where: whereClause
    })

    const leadsByStatus = await prisma.lead.groupBy({
      by: ['status'],
      where: branch ? { branch } : {},
      _count: { _all: true }
    })

    const conversionRate = totalLeads > 0 ? 
      (await prisma.lead.count({ 
        where: { 
          status: "GROUP_ASSIGNED",
          ...(branch && { branch })
        } 
      }) / totalLeads * 100) : 0

    // Group Analytics
    const totalGroups = await prisma.group.count({
      where: {
        isActive: true,
        ...(branch && { branch })
      }
    })

    const groupsByCapacity = await prisma.group.findMany({
      where: {
        isActive: true,
        ...(branch && { branch })
      },
      include: {
        _count: {
          select: {
            studentReferences: true
          }
        }
      }
    })

    const averageGroupSize = groupsByCapacity.length > 0 ? 
      groupsByCapacity.reduce((sum, group) => sum + group._count.studentReferences, 0) / groupsByCapacity.length : 0

    // Payment Analytics
    const totalRevenue = await prisma.paymentOverview.aggregate({
      where: {
        status: "PAID",
        createdAt: { gte: startDate }
      },
      _sum: { amount: true }
    })

    const totalDebt = await prisma.paymentOverview.aggregate({
      where: {
        status: "DEBT"
      },
      _sum: { amount: true }
    })

    const revenueByMonth = await prisma.paymentOverview.findMany({
      where: {
        status: "PAID",
        createdAt: { gte: new Date(new Date().getFullYear(), 0, 1) } // This year
      },
      select: {
        amount: true,
        createdAt: true
      }
    })

    // Group revenue by month
    const monthlyRevenue = Array.from({ length: 12 }, (_, i) => {
      const month = new Date(new Date().getFullYear(), i, 1)
      const monthName = month.toLocaleDateString('en-US', { month: 'short' })
      const monthRevenue = revenueByMonth
        .filter(payment => payment.createdAt.getMonth() === i)
        .reduce((sum, payment) => sum + Number(payment.amount), 0)
      
      return {
        month: monthName,
        revenue: monthRevenue
      }
    })

    // Teacher Analytics
    const totalTeachers = await prisma.teacher.count({
      where: branch ? { branch } : {}
    })

    const teachersByTier = await prisma.teacher.groupBy({
      by: ['tier'],
      where: branch ? { branch } : {},
      _count: { _all: true }
    })

    // Recent Activity
    const recentActivities = await prisma.activityLog.findMany({
      where: {
        createdAt: { gte: startDate }
      },
      include: {
        user: {
          select: {
            name: true
          }
        }
      },
      orderBy: { createdAt: "desc" },
      take: 10
    })

    // Growth trends (comparing with previous period)
    const previousPeriodStart = new Date(startDate)
    previousPeriodStart.setDate(previousPeriodStart.getDate() - parseInt(period))

    const previousPeriodStudents = await prisma.studentReference.count({
      where: {
        createdAt: { 
          gte: previousPeriodStart,
          lt: startDate
        },
        ...(branch && { branch })
      }
    })

    const previousPeriodLeads = await prisma.lead.count({
      where: {
        createdAt: { 
          gte: previousPeriodStart,
          lt: startDate
        },
        ...(branch && { branch })
      }
    })

    const previousPeriodRevenue = await prisma.paymentOverview.aggregate({
      where: {
        status: "PAID",
        createdAt: { 
          gte: previousPeriodStart,
          lt: startDate
        }
      },
      _sum: { amount: true }
    })

    const studentGrowth = previousPeriodStudents > 0 ? 
      ((newStudentsThisPeriod - previousPeriodStudents) / previousPeriodStudents * 100) : 0

    const leadGrowth = previousPeriodLeads > 0 ? 
      ((newLeadsThisPeriod - previousPeriodLeads) / previousPeriodLeads * 100) : 0

    const revenueGrowth = (previousPeriodRevenue._sum.amount || 0) > 0 ? 
      (((totalRevenue._sum.amount || 0) - (previousPeriodRevenue._sum.amount || 0)) / (previousPeriodRevenue._sum.amount || 0) * 100) : 0

    return NextResponse.json({
      overview: {
        totalStudents,
        activeStudents,
        newStudentsThisPeriod,
        studentGrowth,
        totalLeads,
        newLeadsThisPeriod,
        leadGrowth,
        conversionRate,
        totalGroups,
        averageGroupSize,
        totalTeachers,
        totalRevenue: totalRevenue._sum.amount || 0,
        totalDebt: totalDebt._sum.amount || 0,
        revenueGrowth
      },
      charts: {
        studentsByStatus,
        studentsByLevel,
        leadsByStatus,
        teachersByTier,
        monthlyRevenue,
        groupCapacityUtilization: groupsByCapacity.map(group => ({
          name: group.name,
          capacity: group.capacity,
          enrolled: group._count.studentReferences,
          utilization: (group._count.studentReferences / group.capacity * 100)
        }))
      },
      recentActivities: recentActivities.map(activity => ({
        id: activity.id,
        action: activity.action,
        resource: activity.resource,
        userName: activity.user.name,
        createdAt: activity.createdAt,
        details: activity.details
      }))
    })
  } catch (error) {
    console.error("Error fetching analytics:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
