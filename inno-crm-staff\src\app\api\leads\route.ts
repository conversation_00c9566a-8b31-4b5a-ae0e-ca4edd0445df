import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const createLeadSchema = z.object({
  name: z.string().min(1, "Name is required"),
  phone: z.string().min(1, "Phone is required"),
  coursePreference: z.string().min(1, "Course preference is required"),
  source: z.string().optional(),
  notes: z.string().optional(),
  branch: z.string().default("main"),
  followUpDate: z.string().optional().transform((str) => str ? new Date(str) : undefined),
})

const updateLeadSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  phone: z.string().min(1, "Phone is required").optional(),
  coursePreference: z.string().min(1, "Course preference is required").optional(),
  status: z.enum(["NEW", "CALLING", "CALL_COMPLETED", "GROUP_ASSIGNED", "ARCHIVED", "NOT_INTERESTED"]).optional(),
  source: z.string().optional(),
  notes: z.string().optional(),
  branch: z.string().optional(),
  assignedTo: z.string().optional(),
  followUpDate: z.string().optional().transform((str) => str ? new Date(str) : undefined),
  assignedGroupId: z.string().optional(),
  assignedTeacherId: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const search = searchParams.get("search") || ""
    const status = searchParams.get("status") || ""
    const assignedTo = searchParams.get("assignedTo") || ""
    const branch = searchParams.get("branch") || ""

    const skip = (page - 1) * limit

    const where = {
      AND: [
        search ? {
          OR: [
            { name: { contains: search, mode: "insensitive" as const } },
            { phone: { contains: search, mode: "insensitive" as const } },
            { coursePreference: { contains: search, mode: "insensitive" as const } },
          ]
        } : {},
        status ? { status } : {},
        assignedTo ? { assignedTo } : {},
        branch ? { branch } : {}
      ]
    }

    const [leads, total] = await Promise.all([
      prisma.lead.findMany({
        where,
        skip,
        take: limit,
        include: {
          assignedGroup: {
            include: {
              course: true
            }
          },
          assignedTeacher: {
            include: {
              user: true
            }
          },
          callRecords: {
            orderBy: { createdAt: "desc" },
            take: 1,
            include: {
              user: true
            }
          }
        },
        orderBy: { createdAt: "desc" }
      }),
      prisma.lead.count({ where })
    ])

    return NextResponse.json({
      leads,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error("Error fetching leads:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "RECEPTION", "TEACHER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createLeadSchema.parse(body)

    // Check if lead already exists
    const existingLead = await prisma.lead.findFirst({
      where: { phone: validatedData.phone }
    })

    if (existingLead) {
      return NextResponse.json({ error: "Lead with this phone already exists" }, { status: 400 })
    }

    // Create lead
    const lead = await prisma.lead.create({
      data: {
        ...validatedData,
        assignedTo: session.user.id, // Assign to current user by default
      },
      include: {
        assignedGroup: {
          include: {
            course: true
          }
        },
        assignedTeacher: {
          include: {
            user: true
          }
        }
      }
    })

    return NextResponse.json(lead, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error creating lead:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
