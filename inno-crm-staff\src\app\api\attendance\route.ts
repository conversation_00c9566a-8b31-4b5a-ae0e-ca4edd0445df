import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const createAttendanceSchema = z.object({
  groupId: z.string().min(1, "Group is required"),
  date: z.string().transform((str) => new Date(str)),
  studentAttendances: z.array(z.object({
    studentId: z.string(),
    status: z.enum(["PRESENT", "ABSENT", "LATE", "EXCUSED"]),
    notes: z.string().optional()
  })),
  lessonTopic: z.string().optional(),
  homework: z.string().optional(),
  notes: z.string().optional()
})

const updateAttendanceSchema = z.object({
  studentAttendances: z.array(z.object({
    studentId: z.string(),
    status: z.enum(["PRESENT", "ABSENT", "LATE", "EXCUSED"]),
    notes: z.string().optional()
  })).optional(),
  lessonTopic: z.string().optional(),
  homework: z.string().optional(),
  notes: z.string().optional()
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const groupId = searchParams.get("groupId") || ""
    const teacherId = searchParams.get("teacherId") || ""
    const dateFrom = searchParams.get("dateFrom")
    const dateTo = searchParams.get("dateTo")
    const studentId = searchParams.get("studentId") || ""

    const skip = (page - 1) * limit

    let whereClause: any = {}

    // Filter by group
    if (groupId) {
      whereClause.groupId = groupId
    }

    // Filter by teacher (through group)
    if (teacherId) {
      whereClause.group = {
        teacherId: teacherId
      }
    }

    // Filter by date range
    if (dateFrom || dateTo) {
      whereClause.date = {}
      if (dateFrom) whereClause.date.gte = new Date(dateFrom)
      if (dateTo) whereClause.date.lte = new Date(dateTo)
    }

    // If requesting for specific student, get their attendance records
    if (studentId) {
      const attendanceRecords = await prisma.attendanceRecord.findMany({
        where: {
          ...whereClause,
          studentAttendances: {
            some: {
              studentReferenceId: studentId
            }
          }
        },
        skip,
        take: limit,
        include: {
          group: {
            include: {
              course: true,
              teacher: {
                include: {
                  user: true
                }
              }
            }
          },
          studentAttendances: {
            where: {
              studentReferenceId: studentId
            },
            include: {
              studentReference: true
            }
          }
        },
        orderBy: { date: "desc" }
      })

      const total = await prisma.attendanceRecord.count({
        where: {
          ...whereClause,
          studentAttendances: {
            some: {
              studentReferenceId: studentId
            }
          }
        }
      })

      return NextResponse.json({
        attendanceRecords,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      })
    }

    // Get attendance records with all student attendances
    const [attendanceRecords, total] = await Promise.all([
      prisma.attendanceRecord.findMany({
        where: whereClause,
        skip,
        take: limit,
        include: {
          group: {
            include: {
              course: true,
              teacher: {
                include: {
                  user: true
                }
              }
            }
          },
          studentAttendances: {
            include: {
              studentReference: true
            }
          }
        },
        orderBy: { date: "desc" }
      }),
      prisma.attendanceRecord.count({ where: whereClause })
    ])

    return NextResponse.json({
      attendanceRecords,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error("Error fetching attendance:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "TEACHER", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createAttendanceSchema.parse(body)

    // Verify group exists and user has permission
    const group = await prisma.group.findUnique({
      where: { id: validatedData.groupId },
      include: {
        teacher: {
          include: {
            user: true
          }
        }
      }
    })

    if (!group) {
      return NextResponse.json({ error: "Group not found" }, { status: 404 })
    }

    // Check if user is the teacher of this group or has admin privileges
    if (session.user.role === "TEACHER" && group.teacher.user.id !== session.user.id) {
      return NextResponse.json({ error: "You can only take attendance for your own groups" }, { status: 403 })
    }

    // Check if attendance already exists for this date
    const existingAttendance = await prisma.attendanceRecord.findFirst({
      where: {
        groupId: validatedData.groupId,
        date: validatedData.date
      }
    })

    if (existingAttendance) {
      return NextResponse.json({ error: "Attendance already recorded for this date" }, { status: 400 })
    }

    // Create attendance record with student attendances
    const attendanceRecord = await prisma.attendanceRecord.create({
      data: {
        groupId: validatedData.groupId,
        date: validatedData.date,
        lessonTopic: validatedData.lessonTopic,
        homework: validatedData.homework,
        notes: validatedData.notes,
        takenBy: session.user.id,
        studentAttendances: {
          create: validatedData.studentAttendances.map(attendance => ({
            studentReferenceId: attendance.studentId,
            status: attendance.status,
            notes: attendance.notes
          }))
        }
      },
      include: {
        group: {
          include: {
            course: true,
            teacher: {
              include: {
                user: true
              }
            }
          }
        },
        studentAttendances: {
          include: {
            studentReference: true
          }
        }
      }
    })

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: session.user.id,
        userRole: session.user.role as any,
        action: "CREATE",
        resource: "attendance",
        resourceId: attendanceRecord.id,
        details: {
          groupName: group.name,
          date: validatedData.date,
          studentCount: validatedData.studentAttendances.length
        }
      }
    })

    return NextResponse.json(attendanceRecord, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error creating attendance:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
