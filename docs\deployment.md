# Deployment Guide

This guide covers deploying the Inno CRM system to production environments.

## 🏗️ Architecture Overview

The system consists of two Next.js applications that need to be deployed separately:
- **Staff Portal** (inno-crm-staff)
- **Student Portal** (inno-crm-students)

Both applications require:
- PostgreSQL databases
- Redis for session storage (recommended)
- File storage for uploads
- SSL certificates

## 🚀 Deployment Options

### Option 1: Vercel (Recommended)

#### Prerequisites
- Vercel account
- PostgreSQL database (Supabase, PlanetScale, or AWS RDS)
- Redis instance (Upstash recommended)

#### Steps

1. **Prepare Environment Variables**
   
   Create production environment variables for both projects:
   
   **Staff Portal (.env.production)**
   ```env
   DATABASE_URL="********************************/crm_staff_prod"
   NEXTAUTH_SECRET="your-production-secret-key"
   NEXTAUTH_URL="https://staff.yourcrm.com"
   STUDENTS_SERVER_URL="https://students.yourcrm.com"
   INTER_SERVER_SECRET="your-inter-server-secret"
   REDIS_URL="redis://user:pass@host:6379"
   ```
   
   **Student Portal (.env.production)**
   ```env
   DATABASE_URL="********************************/crm_students_prod"
   NEXTAUTH_SECRET="your-production-secret-key"
   NEXTAUTH_URL="https://students.yourcrm.com"
   STAFF_SERVER_URL="https://staff.yourcrm.com"
   INTER_SERVER_SECRET="your-inter-server-secret"
   REDIS_URL="redis://user:pass@host:6379"
   ```

2. **Deploy to Vercel**
   ```bash
   # Install Vercel CLI
   npm i -g vercel
   
   # Deploy staff portal
   cd inno-crm-staff
   vercel --prod
   
   # Deploy student portal
   cd ../inno-crm-students
   vercel --prod
   ```

3. **Configure Custom Domains**
   - Set up custom domains in Vercel dashboard
   - Configure DNS records
   - Enable SSL certificates

4. **Run Database Migrations**
   ```bash
   # Staff database
   cd inno-crm-staff
   npx prisma generate
   npx prisma db push
   
   # Student database
   cd ../inno-crm-students
   npx prisma generate
   npx prisma db push
   ```

### Option 2: Docker Deployment

#### Prerequisites
- Docker and Docker Compose
- PostgreSQL server
- Reverse proxy (Nginx)

#### Docker Configuration

**docker-compose.yml**
```yaml
version: '3.8'

services:
  staff-portal:
    build:
      context: ./inno-crm-staff
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=${STAFF_DATABASE_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - NEXTAUTH_URL=${STAFF_URL}
      - STUDENTS_SERVER_URL=${STUDENTS_URL}
    depends_on:
      - postgres
      - redis

  student-portal:
    build:
      context: ./inno-crm-students
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - DATABASE_URL=${STUDENTS_DATABASE_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - NEXTAUTH_URL=${STUDENTS_URL}
      - STAFF_SERVER_URL=${STAFF_URL}
    depends_on:
      - postgres
      - redis

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=crm
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - staff-portal
      - student-portal

volumes:
  postgres_data:
  redis_data:
```

**Dockerfile (for both projects)**
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci --only=production

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build the application
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

### Option 3: AWS Deployment

#### Using AWS App Runner

1. **Create ECR Repositories**
   ```bash
   aws ecr create-repository --repository-name crm-staff
   aws ecr create-repository --repository-name crm-students
   ```

2. **Build and Push Images**
   ```bash
   # Build and push staff portal
   docker build -t crm-staff ./inno-crm-staff
   docker tag crm-staff:latest <account-id>.dkr.ecr.<region>.amazonaws.com/crm-staff:latest
   docker push <account-id>.dkr.ecr.<region>.amazonaws.com/crm-staff:latest
   
   # Build and push student portal
   docker build -t crm-students ./inno-crm-students
   docker tag crm-students:latest <account-id>.dkr.ecr.<region>.amazonaws.com/crm-students:latest
   docker push <account-id>.dkr.ecr.<region>.amazonaws.com/crm-students:latest
   ```

3. **Create App Runner Services**
   - Use AWS Console or CLI to create App Runner services
   - Configure environment variables
   - Set up custom domains

#### Using ECS with Fargate

1. **Create Task Definitions**
2. **Set up Load Balancer**
3. **Configure Auto Scaling**
4. **Set up CloudWatch Monitoring**

## 🗄️ Database Setup

### PostgreSQL Configuration

**Recommended Settings for Production:**
```sql
-- Performance settings
shared_preload_libraries = 'pg_stat_statements'
max_connections = 100
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

-- Security settings
ssl = on
log_statement = 'mod'
log_min_duration_statement = 1000
```

### Database Migration Strategy

1. **Backup Current Database**
   ```bash
   pg_dump -h localhost -U username -d database_name > backup.sql
   ```

2. **Run Migrations**
   ```bash
   npx prisma migrate deploy
   ```

3. **Verify Migration**
   ```bash
   npx prisma db pull
   npx prisma generate
   ```

## 🔒 Security Configuration

### SSL/TLS Setup

1. **Obtain SSL Certificates**
   - Use Let's Encrypt for free certificates
   - Or purchase from a trusted CA

2. **Configure Nginx**
   ```nginx
   server {
       listen 443 ssl http2;
       server_name staff.yourcrm.com;
       
       ssl_certificate /etc/nginx/ssl/staff.crt;
       ssl_certificate_key /etc/nginx/ssl/staff.key;
       
       location / {
           proxy_pass http://localhost:3000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

### Environment Security

1. **Use Strong Secrets**
   ```bash
   # Generate secure secrets
   openssl rand -base64 32
   ```

2. **Configure CORS**
   ```javascript
   // next.config.js
   module.exports = {
     async headers() {
       return [
         {
           source: '/api/:path*',
           headers: [
             { key: 'Access-Control-Allow-Origin', value: 'https://yourcrm.com' },
             { key: 'Access-Control-Allow-Methods', value: 'GET,POST,PUT,DELETE' },
           ],
         },
       ]
     },
   }
   ```

## 📊 Monitoring & Logging

### Application Monitoring

1. **Set up Error Tracking**
   - Sentry for error monitoring
   - LogRocket for session replay

2. **Performance Monitoring**
   - New Relic or DataDog
   - Custom metrics with Prometheus

3. **Health Checks**
   ```javascript
   // pages/api/health.js
   export default function handler(req, res) {
     res.status(200).json({ 
       status: 'healthy',
       timestamp: new Date().toISOString(),
       version: process.env.npm_package_version
     })
   }
   ```

### Log Management

1. **Structured Logging**
   ```javascript
   import winston from 'winston'
   
   const logger = winston.createLogger({
     level: 'info',
     format: winston.format.json(),
     transports: [
       new winston.transports.File({ filename: 'error.log', level: 'error' }),
       new winston.transports.File({ filename: 'combined.log' })
     ]
   })
   ```

2. **Log Aggregation**
   - ELK Stack (Elasticsearch, Logstash, Kibana)
   - AWS CloudWatch Logs
   - Datadog Logs

## 🚀 Performance Optimization

### Caching Strategy

1. **Redis Configuration**
   ```javascript
   // lib/redis.js
   import Redis from 'ioredis'
   
   const redis = new Redis(process.env.REDIS_URL)
   
   export const cache = {
     get: (key) => redis.get(key),
     set: (key, value, ttl = 3600) => redis.setex(key, ttl, value),
     del: (key) => redis.del(key)
   }
   ```

2. **Database Optimization**
   - Add appropriate indexes
   - Use connection pooling
   - Implement query optimization

### CDN Setup

1. **Static Assets**
   - Use Vercel's built-in CDN
   - Or configure CloudFront for AWS

2. **Image Optimization**
   ```javascript
   // next.config.js
   module.exports = {
     images: {
       domains: ['your-cdn-domain.com'],
       formats: ['image/webp', 'image/avif'],
     },
   }
   ```

## 🔄 CI/CD Pipeline

### GitHub Actions Example

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm test

  deploy-staff:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.STAFF_PROJECT_ID }}
          working-directory: ./inno-crm-staff

  deploy-students:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.STUDENTS_PROJECT_ID }}
          working-directory: ./inno-crm-students
```

## 🆘 Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Check connection strings
   - Verify firewall settings
   - Test database connectivity

2. **Inter-server Communication**
   - Verify CORS settings
   - Check network connectivity
   - Validate API endpoints

3. **Performance Issues**
   - Monitor database queries
   - Check memory usage
   - Analyze slow endpoints

### Rollback Strategy

1. **Database Rollback**
   ```bash
   # Restore from backup
   psql -h localhost -U username -d database_name < backup.sql
   ```

2. **Application Rollback**
   - Use Vercel's instant rollback
   - Or redeploy previous Docker image

## 📋 Post-Deployment Checklist

- [ ] Verify both applications are accessible
- [ ] Test user authentication
- [ ] Check database connections
- [ ] Verify inter-server communication
- [ ] Test critical user flows
- [ ] Monitor error rates
- [ ] Check performance metrics
- [ ] Verify SSL certificates
- [ ] Test backup and restore procedures
- [ ] Update DNS records if needed
