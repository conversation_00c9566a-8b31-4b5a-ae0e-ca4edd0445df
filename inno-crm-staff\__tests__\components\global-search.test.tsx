import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import GlobalSearch from '@/components/search/global-search'

// Mock the useRouter hook
const mockPush = jest.fn()
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

// Mock the useDebounce hook
jest.mock('@/hooks/use-debounce', () => ({
  useDebounce: (value: string) => value,
}))

// Mock fetch
const mockFetch = jest.fn()
global.fetch = mockFetch

describe('GlobalSearch', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders search input with placeholder', () => {
    render(<GlobalSearch placeholder="Search everything..." />)
    
    const input = screen.getByPlaceholderText('Search everything...')
    expect(input).toBeInTheDocument()
  })

  it('shows loading state when searching', async () => {
    const user = userEvent.setup()
    
    // Mock a delayed response
    mockFetch.mockImplementation(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({
          ok: true,
          json: () => Promise.resolve({ results: [] })
        }), 100)
      )
    )

    render(<GlobalSearch />)
    
    const input = screen.getByPlaceholderText('Search everything...')
    await user.type(input, 'test search')
    
    // Should show loading spinner
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })

  it('displays search results when available', async () => {
    const user = userEvent.setup()
    
    const mockResults = [
      {
        id: 'student-1',
        type: 'student',
        title: 'John Doe',
        subtitle: '1234567890',
        description: 'Group A1-1 - English A1',
        url: '/dashboard/students/student-1',
        relevance: 100,
      },
      {
        id: 'group-1',
        type: 'group',
        title: 'Group A1-1',
        subtitle: 'English A1',
        description: 'Teacher Name - 15 students',
        url: '/dashboard/groups/group-1',
        relevance: 80,
      },
    ]

    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ results: mockResults }),
    })

    render(<GlobalSearch />)
    
    const input = screen.getByPlaceholderText('Search everything...')
    await user.click(input)
    await user.type(input, 'john')

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('Group A1-1')).toBeInTheDocument()
    })

    // Check if badges are displayed
    expect(screen.getByText('Student')).toBeInTheDocument()
    expect(screen.getByText('Group')).toBeInTheDocument()
  })

  it('navigates to result URL when clicked', async () => {
    const user = userEvent.setup()
    
    const mockResults = [
      {
        id: 'student-1',
        type: 'student',
        title: 'John Doe',
        subtitle: '1234567890',
        description: 'Group A1-1 - English A1',
        url: '/dashboard/students/student-1',
        relevance: 100,
      },
    ]

    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ results: mockResults }),
    })

    render(<GlobalSearch />)
    
    const input = screen.getByPlaceholderText('Search everything...')
    await user.click(input)
    await user.type(input, 'john')

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
    })

    // Click on the result
    await user.click(screen.getByText('John Doe'))

    expect(mockPush).toHaveBeenCalledWith('/dashboard/students/student-1')
  })

  it('filters results by type', async () => {
    const user = userEvent.setup()
    
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ results: [] }),
    })

    render(<GlobalSearch />)
    
    const input = screen.getByPlaceholderText('Search everything...')
    await user.click(input)
    await user.type(input, 'test')

    await waitFor(() => {
      expect(screen.getByText('All')).toBeInTheDocument()
    })

    // Click on Student filter
    await user.click(screen.getByText('Student'))

    // Check if fetch was called with the correct parameters
    expect(mockFetch).toHaveBeenCalledWith(
      expect.stringContaining('types=student')
    )
  })

  it('shows no results message when search returns empty', async () => {
    const user = userEvent.setup()
    
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ results: [] }),
    })

    render(<GlobalSearch />)
    
    const input = screen.getByPlaceholderText('Search everything...')
    await user.click(input)
    await user.type(input, 'nonexistent')

    await waitFor(() => {
      expect(screen.getByText(/No results found for "nonexistent"/)).toBeInTheDocument()
    })
  })

  it('handles search errors gracefully', async () => {
    const user = userEvent.setup()
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
    
    mockFetch.mockRejectedValue(new Error('Network error'))

    render(<GlobalSearch />)
    
    const input = screen.getByPlaceholderText('Search everything...')
    await user.click(input)
    await user.type(input, 'test')

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Error performing search:', expect.any(Error))
    })

    consoleSpy.mockRestore()
  })

  it('renders as dialog when showDialog prop is true', () => {
    render(<GlobalSearch showDialog={true} />)
    
    expect(screen.getByRole('button')).toBeInTheDocument()
    expect(screen.getByText('Search everything...')).toBeInTheDocument()
  })

  it('closes search results when clicking outside', async () => {
    const user = userEvent.setup()
    
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ results: [] }),
    })

    render(
      <div>
        <GlobalSearch />
        <div data-testid="outside-element">Outside</div>
      </div>
    )
    
    const input = screen.getByPlaceholderText('Search everything...')
    await user.click(input)
    await user.type(input, 'test')

    // Results should be visible
    await waitFor(() => {
      expect(screen.getByText('All')).toBeInTheDocument()
    })

    // Click outside
    await user.click(screen.getByTestId('outside-element'))

    // Results should be hidden
    expect(screen.queryByText('All')).not.toBeInTheDocument()
  })
})
