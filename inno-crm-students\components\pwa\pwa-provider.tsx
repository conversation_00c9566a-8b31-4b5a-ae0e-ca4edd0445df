"use client"

import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { X, Download, Wifi, WifiOff } from 'lucide-react'

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[]
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed'
    platform: string
  }>
  prompt(): Promise<void>
}

export function PWAProvider({ children }: { children: React.ReactNode }) {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)
  const [showInstallPrompt, setShowInstallPrompt] = useState(false)
  const [isOnline, setIsOnline] = useState(true)
  const [showOfflineNotice, setShowOfflineNotice] = useState(false)

  useEffect(() => {
    // Register service worker
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker
        .register('/sw.js')
        .then((registration) => {
          console.log('SW registered: ', registration)
        })
        .catch((registrationError) => {
          console.log('SW registration failed: ', registrationError)
        })
    }

    // Handle install prompt
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setDeferredPrompt(e as BeforeInstallPromptEvent)
      setShowInstallPrompt(true)
    }

    // Handle online/offline status
    const handleOnline = () => {
      setIsOnline(true)
      setShowOfflineNotice(false)
    }

    const handleOffline = () => {
      setIsOnline(false)
      setShowOfflineNotice(true)
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Check initial online status
    setIsOnline(navigator.onLine)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  const handleInstallClick = async () => {
    if (!deferredPrompt) return

    deferredPrompt.prompt()
    const { outcome } = await deferredPrompt.userChoice
    
    if (outcome === 'accepted') {
      console.log('User accepted the install prompt')
    } else {
      console.log('User dismissed the install prompt')
    }
    
    setDeferredPrompt(null)
    setShowInstallPrompt(false)
  }

  const dismissInstallPrompt = () => {
    setShowInstallPrompt(false)
    setDeferredPrompt(null)
  }

  const dismissOfflineNotice = () => {
    setShowOfflineNotice(false)
  }

  return (
    <>
      {children}
      
      {/* Install App Prompt */}
      {showInstallPrompt && (
        <div className="fixed bottom-4 left-4 right-4 z-50 bg-white border border-blue-200 rounded-lg shadow-lg p-4 md:left-auto md:right-4 md:w-80">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <Download className="h-6 w-6 text-blue-600" />
              </div>
              <div className="flex-1">
                <h3 className="text-sm font-medium text-blue-900">
                  Install Student Portal
                </h3>
                <p className="text-sm text-blue-700">
                  Add to your home screen for quick access
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={dismissInstallPrompt}
              className="h-6 w-6"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <div className="mt-3 flex space-x-2">
            <Button
              onClick={handleInstallClick}
              size="sm"
              className="flex-1"
            >
              Install
            </Button>
            <Button
              variant="outline"
              onClick={dismissInstallPrompt}
              size="sm"
              className="flex-1"
            >
              Not now
            </Button>
          </div>
        </div>
      )}

      {/* Offline Notice */}
      {showOfflineNotice && (
        <div className="fixed top-4 left-4 right-4 z-50 bg-yellow-50 border border-yellow-200 rounded-lg shadow-lg p-4 md:left-auto md:right-4 md:w-80">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <WifiOff className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="flex-1">
                <h3 className="text-sm font-medium text-yellow-800">
                  You're offline
                </h3>
                <p className="text-sm text-yellow-700">
                  Some features may be limited
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={dismissOfflineNotice}
              className="h-6 w-6"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Online Status Indicator */}
      <div className="fixed bottom-4 right-4 z-40">
        {isOnline ? (
          <div className="flex items-center space-x-2 bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs">
            <Wifi className="h-3 w-3" />
            <span>Online</span>
          </div>
        ) : (
          <div className="flex items-center space-x-2 bg-red-100 text-red-800 px-3 py-1 rounded-full text-xs">
            <WifiOff className="h-3 w-3" />
            <span>Offline</span>
          </div>
        )}
      </div>
    </>
  )
}
