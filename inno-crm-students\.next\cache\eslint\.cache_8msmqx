[{"C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\dashboard\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\mobile\\mobile-form.tsx": "2", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\mobile\\mobile-nav.tsx": "3", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\mobile\\mobile-table.tsx": "4", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\providers.tsx": "5", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\pwa\\pwa-provider.tsx": "6", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\button.tsx": "7", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\card.tsx": "8", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\dialog.tsx": "9", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\input.tsx": "10", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\label.tsx": "11", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\select.tsx": "12", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\table.tsx": "13", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\auth\\config.ts": "14", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\database\\prisma.ts": "15", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\utils\\cn.ts": "16", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\announcements\\route.ts": "17", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\assessments\\route.ts": "18", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\assessments\\[id]\\route.ts": "19", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "20", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\inter-server\\assessments\\route.ts": "21", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\inter-server\\assessments\\[id]\\route.ts": "22", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\inter-server\\messages\\broadcast\\route.ts": "23", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\messages\\route.ts": "24", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\messages\\[id]\\route.ts": "25", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\payments\\route.ts": "26", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\sync\\route.ts": "27", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\auth\\error\\page.tsx": "28", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\auth\\signin\\page.tsx": "29", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\assessments\\page.tsx": "30", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\messages\\page.tsx": "31", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\page.tsx": "32", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\payments\\page.tsx": "33", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\sync\\page.tsx": "34", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\layout.tsx": "35", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\offline\\page.tsx": "36", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\page.tsx": "37", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\sync\\client.ts": "38"}, {"size": 5668, "mtime": 1750395675476, "results": "39", "hashOfConfig": "40"}, {"size": 3801, "mtime": 1750399667538, "results": "41", "hashOfConfig": "40"}, {"size": 4057, "mtime": 1750399596635, "results": "42", "hashOfConfig": "40"}, {"size": 3591, "mtime": 1750399810251, "results": "43", "hashOfConfig": "40"}, {"size": 309, "mtime": 1750399357952, "results": "44", "hashOfConfig": "40"}, {"size": 5822, "mtime": 1750399886425, "results": "45", "hashOfConfig": "40"}, {"size": 1837, "mtime": 1750363544323, "results": "46", "hashOfConfig": "40"}, {"size": 1879, "mtime": 1750363563434, "results": "47", "hashOfConfig": "40"}, {"size": 3858, "mtime": 1750363605012, "results": "48", "hashOfConfig": "40"}, {"size": 826, "mtime": 1750363551651, "results": "49", "hashOfConfig": "40"}, {"size": 712, "mtime": 1750363571715, "results": "50", "hashOfConfig": "40"}, {"size": 5617, "mtime": 1750363668560, "results": "51", "hashOfConfig": "40"}, {"size": 2767, "mtime": 1750363587592, "results": "52", "hashOfConfig": "40"}, {"size": 1870, "mtime": 1750363302572, "results": "53", "hashOfConfig": "40"}, {"size": 279, "mtime": 1750363308938, "results": "54", "hashOfConfig": "40"}, {"size": 166, "mtime": 1750363611973, "results": "55", "hashOfConfig": "40"}, {"size": 1459, "mtime": 1750395935941, "results": "56", "hashOfConfig": "40"}, {"size": 5339, "mtime": 1750395350761, "results": "57", "hashOfConfig": "40"}, {"size": 7033, "mtime": 1750395377494, "results": "58", "hashOfConfig": "40"}, {"size": 164, "mtime": 1750363314420, "results": "59", "hashOfConfig": "40"}, {"size": 5254, "mtime": 1750395741048, "results": "60", "hashOfConfig": "40"}, {"size": 5184, "mtime": 1750395763953, "results": "61", "hashOfConfig": "40"}, {"size": 3540, "mtime": 1750396371345, "results": "62", "hashOfConfig": "40"}, {"size": 2293, "mtime": 1750395914969, "results": "63", "hashOfConfig": "40"}, {"size": 1866, "mtime": 1750395925261, "results": "64", "hashOfConfig": "40"}, {"size": 2592, "mtime": 1750393827867, "results": "65", "hashOfConfig": "40"}, {"size": 3500, "mtime": 1750394139720, "results": "66", "hashOfConfig": "40"}, {"size": 1883, "mtime": 1750363758360, "results": "67", "hashOfConfig": "40"}, {"size": 3899, "mtime": 1750363746409, "results": "68", "hashOfConfig": "40"}, {"size": 18757, "mtime": 1750399999581, "results": "69", "hashOfConfig": "40"}, {"size": 16256, "mtime": 1750396290342, "results": "70", "hashOfConfig": "40"}, {"size": 8327, "mtime": 1750364504427, "results": "71", "hashOfConfig": "40"}, {"size": 14424, "mtime": 1750393874644, "results": "72", "hashOfConfig": "40"}, {"size": 13571, "mtime": 1750394186788, "results": "73", "hashOfConfig": "40"}, {"size": 1459, "mtime": 1750397639133, "results": "74", "hashOfConfig": "40"}, {"size": 2708, "mtime": 1750399943963, "results": "75", "hashOfConfig": "40"}, {"size": 827, "mtime": 1750364533071, "results": "76", "hashOfConfig": "40"}, {"size": 11847, "mtime": 1750394120781, "results": "77", "hashOfConfig": "40"}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "19o6u7n", {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\mobile\\mobile-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\mobile\\mobile-nav.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\mobile\\mobile-table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\providers.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\pwa\\pwa-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\input.tsx", ["192"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\auth\\config.ts", ["193"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\database\\prisma.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\utils\\cn.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\announcements\\route.ts", ["194", "195", "196", "197", "198"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\assessments\\route.ts", ["199", "200"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\assessments\\[id]\\route.ts", ["201"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\inter-server\\assessments\\route.ts", ["202", "203"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\inter-server\\assessments\\[id]\\route.ts", ["204"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\inter-server\\messages\\broadcast\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\messages\\route.ts", ["205", "206", "207", "208"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\messages\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\payments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\sync\\route.ts", ["209", "210", "211", "212", "213", "214"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\auth\\error\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\auth\\signin\\page.tsx", ["215"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\assessments\\page.tsx", ["216", "217", "218", "219", "220"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\messages\\page.tsx", ["221", "222"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\page.tsx", ["223"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\payments\\page.tsx", ["224", "225", "226"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\sync\\page.tsx", ["227", "228", "229"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\offline\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\sync\\client.ts", ["230", "231"], [], {"ruleId": "232", "severity": 2, "message": "233", "line": 4, "column": 18, "nodeType": "234", "messageId": "235", "endLine": 4, "endColumn": 28, "suggestions": "236"}, {"ruleId": "237", "severity": 2, "message": "238", "line": 69, "column": 63, "nodeType": "239", "messageId": "240", "endLine": 69, "endColumn": 66, "suggestions": "241"}, {"ruleId": "242", "severity": 2, "message": "243", "line": 4, "column": 10, "nodeType": null, "messageId": "244", "endLine": 4, "endColumn": 16}, {"ruleId": "242", "severity": 2, "message": "245", "line": 18, "column": 11, "nodeType": null, "messageId": "244", "endLine": 18, "endColumn": 15}, {"ruleId": "246", "severity": 2, "message": "247", "line": 20, "column": 9, "nodeType": "234", "messageId": "248", "endLine": 20, "endColumn": 19, "fix": "249"}, {"ruleId": "237", "severity": 2, "message": "238", "line": 20, "column": 16, "nodeType": "239", "messageId": "240", "endLine": 20, "endColumn": 19, "suggestions": "250"}, {"ruleId": "237", "severity": 2, "message": "238", "line": 34, "column": 26, "nodeType": "239", "messageId": "240", "endLine": 34, "endColumn": 29, "suggestions": "251"}, {"ruleId": "246", "severity": 2, "message": "247", "line": 43, "column": 9, "nodeType": "234", "messageId": "248", "endLine": 43, "endColumn": 19, "fix": "252"}, {"ruleId": "237", "severity": 2, "message": "238", "line": 43, "column": 16, "nodeType": "239", "messageId": "240", "endLine": 43, "endColumn": 19, "suggestions": "253"}, {"ruleId": "237", "severity": 2, "message": "238", "line": 166, "column": 38, "nodeType": "239", "messageId": "240", "endLine": 166, "endColumn": 41, "suggestions": "254"}, {"ruleId": "246", "severity": 2, "message": "247", "line": 54, "column": 9, "nodeType": "234", "messageId": "248", "endLine": 54, "endColumn": 19, "fix": "255"}, {"ruleId": "237", "severity": 2, "message": "238", "line": 54, "column": 16, "nodeType": "239", "messageId": "240", "endLine": 54, "endColumn": 19, "suggestions": "256"}, {"ruleId": "237", "severity": 2, "message": "238", "line": 116, "column": 38, "nodeType": "239", "messageId": "240", "endLine": 116, "endColumn": 41, "suggestions": "257"}, {"ruleId": "242", "severity": 2, "message": "258", "line": 5, "column": 10, "nodeType": null, "messageId": "244", "endLine": 5, "endColumn": 11}, {"ruleId": "242", "severity": 2, "message": "259", "line": 17, "column": 11, "nodeType": null, "messageId": "244", "endLine": 17, "endColumn": 17}, {"ruleId": "246", "severity": 2, "message": "247", "line": 32, "column": 9, "nodeType": "234", "messageId": "248", "endLine": 32, "endColumn": 19, "fix": "260"}, {"ruleId": "237", "severity": 2, "message": "238", "line": 32, "column": 16, "nodeType": "239", "messageId": "240", "endLine": 32, "endColumn": 19, "suggestions": "261"}, {"ruleId": "242", "severity": 2, "message": "262", "line": 24, "column": 23, "nodeType": null, "messageId": "244", "endLine": 24, "endColumn": 28}, {"ruleId": "237", "severity": 2, "message": "238", "line": 26, "column": 20, "nodeType": "239", "messageId": "240", "endLine": 26, "endColumn": 23, "suggestions": "263"}, {"ruleId": "237", "severity": 2, "message": "238", "line": 39, "column": 65, "nodeType": "239", "messageId": "240", "endLine": 39, "endColumn": 68, "suggestions": "264"}, {"ruleId": "237", "severity": 2, "message": "238", "line": 40, "column": 77, "nodeType": "239", "messageId": "240", "endLine": 40, "endColumn": 80, "suggestions": "265"}, {"ruleId": "237", "severity": 2, "message": "238", "line": 41, "column": 63, "nodeType": "239", "messageId": "240", "endLine": 41, "endColumn": 66, "suggestions": "266"}, {"ruleId": "242", "severity": 2, "message": "267", "line": 60, "column": 27, "nodeType": null, "messageId": "244", "endLine": 60, "endColumn": 34}, {"ruleId": "242", "severity": 2, "message": "268", "line": 56, "column": 14, "nodeType": null, "messageId": "244", "endLine": 56, "endColumn": 19}, {"ruleId": "242", "severity": 2, "message": "269", "line": 9, "column": 79, "nodeType": null, "messageId": "244", "endLine": 9, "endColumn": 92}, {"ruleId": "242", "severity": 2, "message": "270", "line": 15, "column": 3, "nodeType": null, "messageId": "244", "endLine": 15, "endColumn": 14}, {"ruleId": "242", "severity": 2, "message": "271", "line": 16, "column": 3, "nodeType": null, "messageId": "244", "endLine": 16, "endColumn": 10}, {"ruleId": "237", "severity": 2, "message": "238", "line": 38, "column": 13, "nodeType": "239", "messageId": "240", "endLine": 38, "endColumn": 16, "suggestions": "272"}, {"ruleId": "242", "severity": 2, "message": "273", "line": 74, "column": 17, "nodeType": null, "messageId": "244", "endLine": 74, "endColumn": 24}, {"ruleId": "242", "severity": 2, "message": "269", "line": 11, "column": 79, "nodeType": null, "messageId": "244", "endLine": 11, "endColumn": 92}, {"ruleId": "242", "severity": 2, "message": "273", "line": 51, "column": 17, "nodeType": null, "messageId": "244", "endLine": 51, "endColumn": 24}, {"ruleId": "274", "severity": 2, "message": "275", "line": 143, "column": 43, "nodeType": "276", "messageId": "277", "suggestions": "278"}, {"ruleId": "242", "severity": 2, "message": "279", "line": 10, "column": 3, "nodeType": null, "messageId": "244", "endLine": 10, "endColumn": 13}, {"ruleId": "242", "severity": 2, "message": "273", "line": 61, "column": 17, "nodeType": null, "messageId": "244", "endLine": 61, "endColumn": 24}, {"ruleId": "280", "severity": 1, "message": "281", "line": 98, "column": 6, "nodeType": "282", "endLine": 98, "endColumn": 37, "suggestions": "283"}, {"ruleId": "242", "severity": 2, "message": "284", "line": 12, "column": 3, "nodeType": null, "messageId": "244", "endLine": 12, "endColumn": 8}, {"ruleId": "237", "severity": 2, "message": "238", "line": 41, "column": 22, "nodeType": "239", "messageId": "240", "endLine": 41, "endColumn": 25, "suggestions": "285"}, {"ruleId": "274", "severity": 2, "message": "275", "line": 113, "column": 47, "nodeType": "276", "messageId": "277", "suggestions": "286"}, {"ruleId": "237", "severity": 2, "message": "238", "line": 29, "column": 83, "nodeType": "239", "messageId": "240", "endLine": 29, "endColumn": 86, "suggestions": "287"}, {"ruleId": "237", "severity": 2, "message": "238", "line": 341, "column": 34, "nodeType": "239", "messageId": "240", "endLine": 341, "endColumn": 37, "suggestions": "288"}, "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["289"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["290", "291"], "@typescript-eslint/no-unused-vars", "'prisma' is defined but never used.", "unusedVar", "'skip' is assigned a value but never used.", "prefer-const", "'where' is never reassigned. Use 'const' instead.", "useConst", {"range": "292", "text": "293"}, ["294", "295"], ["296", "297"], {"range": "298", "text": "299"}, ["300", "301"], ["302", "303"], {"range": "304", "text": "299"}, ["305", "306"], ["307", "308"], "'z' is defined but never used.", "'status' is assigned a value but never used.", {"range": "309", "text": "310"}, ["311", "312"], "'force' is assigned a value but never used.", ["313", "314"], ["315", "316"], ["317", "318"], ["319", "320"], "'request' is defined but never used.", "'error' is defined but never used.", "'DialogTrigger' is defined but never used.", "'CheckCircle' is defined but never used.", "'XCircle' is defined but never used.", ["321", "322"], "'session' is assigned a value but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["323", "324", "325", "326"], "'DollarSign' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchPayments'. Either include it or remove the dependency array.", "ArrayExpression", ["327"], "'Clock' is defined but never used.", ["328", "329"], ["330", "331", "332", "333"], ["334", "335"], ["336", "337"], {"messageId": "338", "fix": "339", "desc": "340"}, {"messageId": "341", "fix": "342", "desc": "343"}, {"messageId": "344", "fix": "345", "desc": "346"}, [715, 825], "const where: any = {\n      isActive: true,\n      targetAudience: {\n        in: [\"ALL\", \"STUDENTS\"]\n      }\n    }", {"messageId": "341", "fix": "347", "desc": "343"}, {"messageId": "344", "fix": "348", "desc": "346"}, {"messageId": "341", "fix": "349", "desc": "343"}, {"messageId": "344", "fix": "350", "desc": "346"}, [1732, 1751], "const where: any = {}", {"messageId": "341", "fix": "351", "desc": "343"}, {"messageId": "344", "fix": "352", "desc": "346"}, {"messageId": "341", "fix": "353", "desc": "343"}, {"messageId": "344", "fix": "354", "desc": "346"}, [1978, 1997], {"messageId": "341", "fix": "355", "desc": "343"}, {"messageId": "344", "fix": "356", "desc": "346"}, {"messageId": "341", "fix": "357", "desc": "343"}, {"messageId": "344", "fix": "358", "desc": "346"}, [1080, 1413], "const where: any = {\n      OR: [\n        { recipientType: \"ALL\" },\n        { recipientType: \"STUDENTS\" },\n        { \n          AND: [\n            { recipientType: \"SPECIFIC\" },\n            { recipientIds: { has: user.studentProfile.id } }\n          ]\n        }\n      ],\n      status: \"SENT\" // Only show sent messages to students\n    }", {"messageId": "341", "fix": "359", "desc": "343"}, {"messageId": "344", "fix": "360", "desc": "346"}, {"messageId": "341", "fix": "361", "desc": "343"}, {"messageId": "344", "fix": "362", "desc": "346"}, {"messageId": "341", "fix": "363", "desc": "343"}, {"messageId": "344", "fix": "364", "desc": "346"}, {"messageId": "341", "fix": "365", "desc": "343"}, {"messageId": "344", "fix": "366", "desc": "346"}, {"messageId": "341", "fix": "367", "desc": "343"}, {"messageId": "344", "fix": "368", "desc": "346"}, {"messageId": "341", "fix": "369", "desc": "343"}, {"messageId": "344", "fix": "370", "desc": "346"}, {"messageId": "371", "data": "372", "fix": "373", "desc": "374"}, {"messageId": "371", "data": "375", "fix": "376", "desc": "377"}, {"messageId": "371", "data": "378", "fix": "379", "desc": "380"}, {"messageId": "371", "data": "381", "fix": "382", "desc": "383"}, {"desc": "384", "fix": "385"}, {"messageId": "341", "fix": "386", "desc": "343"}, {"messageId": "344", "fix": "387", "desc": "346"}, {"messageId": "371", "data": "388", "fix": "389", "desc": "374"}, {"messageId": "371", "data": "390", "fix": "391", "desc": "377"}, {"messageId": "371", "data": "392", "fix": "393", "desc": "380"}, {"messageId": "371", "data": "394", "fix": "395", "desc": "383"}, {"messageId": "341", "fix": "396", "desc": "343"}, {"messageId": "344", "fix": "397", "desc": "346"}, {"messageId": "341", "fix": "398", "desc": "343"}, {"messageId": "344", "fix": "399", "desc": "346"}, "replaceEmptyInterfaceWithSuper", {"range": "400", "text": "401"}, "Replace empty interface with a type alias.", "suggestUnknown", {"range": "402", "text": "403"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "404", "text": "405"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "406", "text": "403"}, {"range": "407", "text": "405"}, {"range": "408", "text": "403"}, {"range": "409", "text": "405"}, {"range": "410", "text": "403"}, {"range": "411", "text": "405"}, {"range": "412", "text": "403"}, {"range": "413", "text": "405"}, {"range": "414", "text": "403"}, {"range": "415", "text": "405"}, {"range": "416", "text": "403"}, {"range": "417", "text": "405"}, {"range": "418", "text": "403"}, {"range": "419", "text": "405"}, {"range": "420", "text": "403"}, {"range": "421", "text": "405"}, {"range": "422", "text": "403"}, {"range": "423", "text": "405"}, {"range": "424", "text": "403"}, {"range": "425", "text": "405"}, {"range": "426", "text": "403"}, {"range": "427", "text": "405"}, {"range": "428", "text": "403"}, {"range": "429", "text": "405"}, "replaceWithAlt", {"alt": "430"}, {"range": "431", "text": "432"}, "Replace with `&apos;`.", {"alt": "433"}, {"range": "434", "text": "435"}, "Replace with `&lsquo;`.", {"alt": "436"}, {"range": "437", "text": "438"}, "Replace with `&#39;`.", {"alt": "439"}, {"range": "440", "text": "441"}, "Replace with `&rsquo;`.", "Update the dependencies array to be: [fetchPayments, pagination.page, statusFilter]", {"range": "442", "text": "443"}, {"range": "444", "text": "403"}, {"range": "445", "text": "405"}, {"alt": "430"}, {"range": "446", "text": "447"}, {"alt": "433"}, {"range": "448", "text": "449"}, {"alt": "436"}, {"range": "450", "text": "451"}, {"alt": "439"}, {"range": "452", "text": "453"}, {"range": "454", "text": "403"}, {"range": "455", "text": "405"}, {"range": "456", "text": "403"}, {"range": "457", "text": "405"}, [75, 152], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [1756, 1759], "unknown", [1756, 1759], "never", [726, 729], [726, 729], [1090, 1093], [1090, 1093], [1743, 1746], [1743, 1746], [5299, 5302], [5299, 5302], [1989, 1992], [1989, 1992], [3488, 3491], [3488, 3491], [1091, 1094], [1091, 1094], [881, 884], [881, 884], [1360, 1363], [1360, 1363], [1460, 1463], [1460, 1463], [1559, 1562], [1559, 1562], [1071, 1074], [1071, 1074], "&apos;", [3119, 3168], ", here&apos;s your learning progress today.\n          ", "&lsquo;", [3119, 3168], ", here&lsquo;s your learning progress today.\n          ", "&#39;", [3119, 3168], ", here&#39;s your learning progress today.\n          ", "&rsquo;", [3119, 3168], ", here&rsquo;s your learning progress today.\n          ", [2433, 2464], "[fetchPayments, pagination.page, statusFilter]", [944, 947], [944, 947], [2971, 3023], "You don&apos;t have permission to manage synchronization.", [2971, 3023], "You don&lsquo;t have permission to manage synchronization.", [2971, 3023], "You don&#39;t have permission to manage synchronization.", [2971, 3023], "You don&rsquo;t have permission to manage synchronization.", [630, 633], [630, 633], [11558, 11561], [11558, 11561]]