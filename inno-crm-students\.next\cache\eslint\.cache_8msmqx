[{"C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\dashboard\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\mobile\\mobile-form.tsx": "2", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\mobile\\mobile-nav.tsx": "3", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\mobile\\mobile-table.tsx": "4", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\providers.tsx": "5", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\pwa\\pwa-provider.tsx": "6", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\button.tsx": "7", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\card.tsx": "8", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\dialog.tsx": "9", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\input.tsx": "10", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\label.tsx": "11", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\select.tsx": "12", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\table.tsx": "13", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\auth\\config.ts": "14", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\database\\prisma.ts": "15", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\utils\\cn.ts": "16", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\announcements\\route.ts": "17", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\assessments\\route.ts": "18", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\assessments\\[id]\\route.ts": "19", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "20", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\inter-server\\assessments\\route.ts": "21", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\inter-server\\assessments\\[id]\\route.ts": "22", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\inter-server\\messages\\broadcast\\route.ts": "23", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\messages\\route.ts": "24", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\messages\\[id]\\route.ts": "25", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\payments\\route.ts": "26", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\sync\\route.ts": "27", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\auth\\error\\page.tsx": "28", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\auth\\signin\\page.tsx": "29", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\assessments\\page.tsx": "30", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\messages\\page.tsx": "31", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\page.tsx": "32", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\payments\\page.tsx": "33", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\sync\\page.tsx": "34", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\layout.tsx": "35", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\offline\\page.tsx": "36", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\page.tsx": "37", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\sync\\client.ts": "38"}, {"size": 5668, "mtime": 1750395675476, "results": "39", "hashOfConfig": "40"}, {"size": 3801, "mtime": 1750399667538, "results": "41", "hashOfConfig": "40"}, {"size": 4057, "mtime": 1750399596635, "results": "42", "hashOfConfig": "40"}, {"size": 3639, "mtime": 1750399630254, "results": "43", "hashOfConfig": "40"}, {"size": 309, "mtime": 1750399357952, "results": "44", "hashOfConfig": "40"}, {"size": 5817, "mtime": 1750397714944, "results": "45", "hashOfConfig": "40"}, {"size": 1837, "mtime": 1750363544323, "results": "46", "hashOfConfig": "40"}, {"size": 1879, "mtime": 1750363563434, "results": "47", "hashOfConfig": "40"}, {"size": 3858, "mtime": 1750363605012, "results": "48", "hashOfConfig": "40"}, {"size": 826, "mtime": 1750363551651, "results": "49", "hashOfConfig": "40"}, {"size": 712, "mtime": 1750363571715, "results": "50", "hashOfConfig": "40"}, {"size": 5617, "mtime": 1750363668560, "results": "51", "hashOfConfig": "40"}, {"size": 2767, "mtime": 1750363587592, "results": "52", "hashOfConfig": "40"}, {"size": 1870, "mtime": 1750363302572, "results": "53", "hashOfConfig": "40"}, {"size": 279, "mtime": 1750363308938, "results": "54", "hashOfConfig": "40"}, {"size": 166, "mtime": 1750363611973, "results": "55", "hashOfConfig": "40"}, {"size": 1459, "mtime": 1750395935941, "results": "56", "hashOfConfig": "40"}, {"size": 5339, "mtime": 1750395350761, "results": "57", "hashOfConfig": "40"}, {"size": 7033, "mtime": 1750395377494, "results": "58", "hashOfConfig": "40"}, {"size": 164, "mtime": 1750363314420, "results": "59", "hashOfConfig": "40"}, {"size": 5254, "mtime": 1750395741048, "results": "60", "hashOfConfig": "40"}, {"size": 5184, "mtime": 1750395763953, "results": "61", "hashOfConfig": "40"}, {"size": 3540, "mtime": 1750396371345, "results": "62", "hashOfConfig": "40"}, {"size": 2293, "mtime": 1750395914969, "results": "63", "hashOfConfig": "40"}, {"size": 1866, "mtime": 1750395925261, "results": "64", "hashOfConfig": "40"}, {"size": 2592, "mtime": 1750393827867, "results": "65", "hashOfConfig": "40"}, {"size": 3500, "mtime": 1750394139720, "results": "66", "hashOfConfig": "40"}, {"size": 1883, "mtime": 1750363758360, "results": "67", "hashOfConfig": "40"}, {"size": 3899, "mtime": 1750363746409, "results": "68", "hashOfConfig": "40"}, {"size": 18742, "mtime": 1750395594551, "results": "69", "hashOfConfig": "40"}, {"size": 16256, "mtime": 1750396290342, "results": "70", "hashOfConfig": "40"}, {"size": 8327, "mtime": 1750364504427, "results": "71", "hashOfConfig": "40"}, {"size": 14424, "mtime": 1750393874644, "results": "72", "hashOfConfig": "40"}, {"size": 13571, "mtime": 1750394186788, "results": "73", "hashOfConfig": "40"}, {"size": 1459, "mtime": 1750397639133, "results": "74", "hashOfConfig": "40"}, {"size": 2708, "mtime": 1750399558544, "results": "75", "hashOfConfig": "40"}, {"size": 827, "mtime": 1750364533071, "results": "76", "hashOfConfig": "40"}, {"size": 11847, "mtime": 1750394120781, "results": "77", "hashOfConfig": "40"}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "19o6u7n", {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\mobile\\mobile-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\mobile\\mobile-nav.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\mobile\\mobile-table.tsx", ["192"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\providers.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\pwa\\pwa-provider.tsx", ["193"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\input.tsx", ["194"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\auth\\config.ts", ["195"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\database\\prisma.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\utils\\cn.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\announcements\\route.ts", ["196", "197", "198", "199", "200"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\assessments\\route.ts", ["201", "202"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\assessments\\[id]\\route.ts", ["203"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\inter-server\\assessments\\route.ts", ["204", "205"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\inter-server\\assessments\\[id]\\route.ts", ["206"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\inter-server\\messages\\broadcast\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\messages\\route.ts", ["207", "208", "209", "210"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\messages\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\payments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\sync\\route.ts", ["211", "212", "213", "214", "215", "216"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\auth\\error\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\auth\\signin\\page.tsx", ["217"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\assessments\\page.tsx", ["218", "219", "220", "221", "222", "223", "224", "225"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\messages\\page.tsx", ["226", "227"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\page.tsx", ["228"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\payments\\page.tsx", ["229", "230", "231"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\sync\\page.tsx", ["232", "233", "234"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\offline\\page.tsx", ["235", "236"], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\sync\\client.ts", ["237", "238"], [], {"ruleId": "239", "severity": 2, "message": "240", "line": 5, "column": 10, "nodeType": null, "messageId": "241", "endLine": 5, "endColumn": 16}, {"ruleId": "242", "severity": 2, "message": "243", "line": 152, "column": 22, "nodeType": "244", "messageId": "245", "suggestions": "246"}, {"ruleId": "247", "severity": 2, "message": "248", "line": 4, "column": 18, "nodeType": "249", "messageId": "250", "endLine": 4, "endColumn": 28, "suggestions": "251"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 69, "column": 63, "nodeType": "254", "messageId": "255", "endLine": 69, "endColumn": 66, "suggestions": "256"}, {"ruleId": "239", "severity": 2, "message": "257", "line": 4, "column": 10, "nodeType": null, "messageId": "241", "endLine": 4, "endColumn": 16}, {"ruleId": "239", "severity": 2, "message": "258", "line": 18, "column": 11, "nodeType": null, "messageId": "241", "endLine": 18, "endColumn": 15}, {"ruleId": "259", "severity": 2, "message": "260", "line": 20, "column": 9, "nodeType": "249", "messageId": "261", "endLine": 20, "endColumn": 19, "fix": "262"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 20, "column": 16, "nodeType": "254", "messageId": "255", "endLine": 20, "endColumn": 19, "suggestions": "263"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 34, "column": 26, "nodeType": "254", "messageId": "255", "endLine": 34, "endColumn": 29, "suggestions": "264"}, {"ruleId": "259", "severity": 2, "message": "260", "line": 43, "column": 9, "nodeType": "249", "messageId": "261", "endLine": 43, "endColumn": 19, "fix": "265"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 43, "column": 16, "nodeType": "254", "messageId": "255", "endLine": 43, "endColumn": 19, "suggestions": "266"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 166, "column": 38, "nodeType": "254", "messageId": "255", "endLine": 166, "endColumn": 41, "suggestions": "267"}, {"ruleId": "259", "severity": 2, "message": "260", "line": 54, "column": 9, "nodeType": "249", "messageId": "261", "endLine": 54, "endColumn": 19, "fix": "268"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 54, "column": 16, "nodeType": "254", "messageId": "255", "endLine": 54, "endColumn": 19, "suggestions": "269"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 116, "column": 38, "nodeType": "254", "messageId": "255", "endLine": 116, "endColumn": 41, "suggestions": "270"}, {"ruleId": "239", "severity": 2, "message": "271", "line": 5, "column": 10, "nodeType": null, "messageId": "241", "endLine": 5, "endColumn": 11}, {"ruleId": "239", "severity": 2, "message": "272", "line": 17, "column": 11, "nodeType": null, "messageId": "241", "endLine": 17, "endColumn": 17}, {"ruleId": "259", "severity": 2, "message": "260", "line": 32, "column": 9, "nodeType": "249", "messageId": "261", "endLine": 32, "endColumn": 19, "fix": "273"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 32, "column": 16, "nodeType": "254", "messageId": "255", "endLine": 32, "endColumn": 19, "suggestions": "274"}, {"ruleId": "239", "severity": 2, "message": "275", "line": 24, "column": 23, "nodeType": null, "messageId": "241", "endLine": 24, "endColumn": 28}, {"ruleId": "252", "severity": 2, "message": "253", "line": 26, "column": 20, "nodeType": "254", "messageId": "255", "endLine": 26, "endColumn": 23, "suggestions": "276"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 39, "column": 65, "nodeType": "254", "messageId": "255", "endLine": 39, "endColumn": 68, "suggestions": "277"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 40, "column": 77, "nodeType": "254", "messageId": "255", "endLine": 40, "endColumn": 80, "suggestions": "278"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 41, "column": 63, "nodeType": "254", "messageId": "255", "endLine": 41, "endColumn": 66, "suggestions": "279"}, {"ruleId": "239", "severity": 2, "message": "280", "line": 60, "column": 27, "nodeType": null, "messageId": "241", "endLine": 60, "endColumn": 34}, {"ruleId": "239", "severity": 2, "message": "281", "line": 56, "column": 14, "nodeType": null, "messageId": "241", "endLine": 56, "endColumn": 19}, {"ruleId": "239", "severity": 2, "message": "282", "line": 9, "column": 79, "nodeType": null, "messageId": "241", "endLine": 9, "endColumn": 92}, {"ruleId": "239", "severity": 2, "message": "283", "line": 15, "column": 3, "nodeType": null, "messageId": "241", "endLine": 15, "endColumn": 14}, {"ruleId": "239", "severity": 2, "message": "284", "line": 16, "column": 3, "nodeType": null, "messageId": "241", "endLine": 16, "endColumn": 10}, {"ruleId": "252", "severity": 2, "message": "253", "line": 38, "column": 13, "nodeType": "254", "messageId": "255", "endLine": 38, "endColumn": 16, "suggestions": "285"}, {"ruleId": "239", "severity": 2, "message": "286", "line": 74, "column": 17, "nodeType": null, "messageId": "241", "endLine": 74, "endColumn": 24}, {"ruleId": "242", "severity": 2, "message": "243", "line": 321, "column": 55, "nodeType": "244", "messageId": "245", "suggestions": "287"}, {"ruleId": "242", "severity": 2, "message": "243", "line": 364, "column": 32, "nodeType": "244", "messageId": "245", "suggestions": "288"}, {"ruleId": "242", "severity": 2, "message": "243", "line": 364, "column": 53, "nodeType": "244", "messageId": "245", "suggestions": "289"}, {"ruleId": "239", "severity": 2, "message": "282", "line": 11, "column": 79, "nodeType": null, "messageId": "241", "endLine": 11, "endColumn": 92}, {"ruleId": "239", "severity": 2, "message": "286", "line": 51, "column": 17, "nodeType": null, "messageId": "241", "endLine": 51, "endColumn": 24}, {"ruleId": "242", "severity": 2, "message": "243", "line": 143, "column": 43, "nodeType": "244", "messageId": "245", "suggestions": "290"}, {"ruleId": "239", "severity": 2, "message": "291", "line": 10, "column": 3, "nodeType": null, "messageId": "241", "endLine": 10, "endColumn": 13}, {"ruleId": "239", "severity": 2, "message": "286", "line": 61, "column": 17, "nodeType": null, "messageId": "241", "endLine": 61, "endColumn": 24}, {"ruleId": "292", "severity": 1, "message": "293", "line": 98, "column": 6, "nodeType": "294", "endLine": 98, "endColumn": 37, "suggestions": "295"}, {"ruleId": "239", "severity": 2, "message": "296", "line": 12, "column": 3, "nodeType": null, "messageId": "241", "endLine": 12, "endColumn": 8}, {"ruleId": "252", "severity": 2, "message": "253", "line": 41, "column": 22, "nodeType": "254", "messageId": "255", "endLine": 41, "endColumn": 25, "suggestions": "297"}, {"ruleId": "242", "severity": 2, "message": "243", "line": 113, "column": 47, "nodeType": "244", "messageId": "245", "suggestions": "298"}, {"ruleId": "242", "severity": 2, "message": "243", "line": 48, "column": 16, "nodeType": "244", "messageId": "245", "suggestions": "299"}, {"ruleId": "242", "severity": 2, "message": "243", "line": 52, "column": 30, "nodeType": "244", "messageId": "245", "suggestions": "300"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 29, "column": 83, "nodeType": "254", "messageId": "255", "endLine": 29, "endColumn": 86, "suggestions": "301"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 341, "column": 34, "nodeType": "254", "messageId": "255", "endLine": 341, "endColumn": 37, "suggestions": "302"}, "@typescript-eslint/no-unused-vars", "'Button' is defined but never used.", "unusedVar", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["303", "304", "305", "306"], "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["307"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["308", "309"], "'prisma' is defined but never used.", "'skip' is assigned a value but never used.", "prefer-const", "'where' is never reassigned. Use 'const' instead.", "useConst", {"range": "310", "text": "311"}, ["312", "313"], ["314", "315"], {"range": "316", "text": "317"}, ["318", "319"], ["320", "321"], {"range": "322", "text": "317"}, ["323", "324"], ["325", "326"], "'z' is defined but never used.", "'status' is assigned a value but never used.", {"range": "327", "text": "328"}, ["329", "330"], "'force' is assigned a value but never used.", ["331", "332"], ["333", "334"], ["335", "336"], ["337", "338"], "'request' is defined but never used.", "'error' is defined but never used.", "'DialogTrigger' is defined but never used.", "'CheckCircle' is defined but never used.", "'XCircle' is defined but never used.", ["339", "340"], "'session' is assigned a value but never used.", ["341", "342", "343", "344"], ["345", "346", "347", "348"], ["349", "350", "351", "352"], ["353", "354", "355", "356"], "'DollarSign' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchPayments'. Either include it or remove the dependency array.", "ArrayExpression", ["357"], "'Clock' is defined but never used.", ["358", "359"], ["360", "361", "362", "363"], ["364", "365", "366", "367"], ["368", "369", "370", "371"], ["372", "373"], ["374", "375"], {"messageId": "376", "data": "377", "fix": "378", "desc": "379"}, {"messageId": "376", "data": "380", "fix": "381", "desc": "382"}, {"messageId": "376", "data": "383", "fix": "384", "desc": "385"}, {"messageId": "376", "data": "386", "fix": "387", "desc": "388"}, {"messageId": "389", "fix": "390", "desc": "391"}, {"messageId": "392", "fix": "393", "desc": "394"}, {"messageId": "395", "fix": "396", "desc": "397"}, [715, 825], "const where: any = {\n      isActive: true,\n      targetAudience: {\n        in: [\"ALL\", \"STUDENTS\"]\n      }\n    }", {"messageId": "392", "fix": "398", "desc": "394"}, {"messageId": "395", "fix": "399", "desc": "397"}, {"messageId": "392", "fix": "400", "desc": "394"}, {"messageId": "395", "fix": "401", "desc": "397"}, [1732, 1751], "const where: any = {}", {"messageId": "392", "fix": "402", "desc": "394"}, {"messageId": "395", "fix": "403", "desc": "397"}, {"messageId": "392", "fix": "404", "desc": "394"}, {"messageId": "395", "fix": "405", "desc": "397"}, [1978, 1997], {"messageId": "392", "fix": "406", "desc": "394"}, {"messageId": "395", "fix": "407", "desc": "397"}, {"messageId": "392", "fix": "408", "desc": "394"}, {"messageId": "395", "fix": "409", "desc": "397"}, [1080, 1413], "const where: any = {\n      OR: [\n        { recipientType: \"ALL\" },\n        { recipientType: \"STUDENTS\" },\n        { \n          AND: [\n            { recipientType: \"SPECIFIC\" },\n            { recipientIds: { has: user.studentProfile.id } }\n          ]\n        }\n      ],\n      status: \"SENT\" // Only show sent messages to students\n    }", {"messageId": "392", "fix": "410", "desc": "394"}, {"messageId": "395", "fix": "411", "desc": "397"}, {"messageId": "392", "fix": "412", "desc": "394"}, {"messageId": "395", "fix": "413", "desc": "397"}, {"messageId": "392", "fix": "414", "desc": "394"}, {"messageId": "395", "fix": "415", "desc": "397"}, {"messageId": "392", "fix": "416", "desc": "394"}, {"messageId": "395", "fix": "417", "desc": "397"}, {"messageId": "392", "fix": "418", "desc": "394"}, {"messageId": "395", "fix": "419", "desc": "397"}, {"messageId": "392", "fix": "420", "desc": "394"}, {"messageId": "395", "fix": "421", "desc": "397"}, {"messageId": "376", "data": "422", "fix": "423", "desc": "379"}, {"messageId": "376", "data": "424", "fix": "425", "desc": "382"}, {"messageId": "376", "data": "426", "fix": "427", "desc": "385"}, {"messageId": "376", "data": "428", "fix": "429", "desc": "388"}, {"messageId": "376", "data": "430", "fix": "431", "desc": "379"}, {"messageId": "376", "data": "432", "fix": "433", "desc": "382"}, {"messageId": "376", "data": "434", "fix": "435", "desc": "385"}, {"messageId": "376", "data": "436", "fix": "437", "desc": "388"}, {"messageId": "376", "data": "438", "fix": "439", "desc": "379"}, {"messageId": "376", "data": "440", "fix": "441", "desc": "382"}, {"messageId": "376", "data": "442", "fix": "443", "desc": "385"}, {"messageId": "376", "data": "444", "fix": "445", "desc": "388"}, {"messageId": "376", "data": "446", "fix": "447", "desc": "379"}, {"messageId": "376", "data": "448", "fix": "449", "desc": "382"}, {"messageId": "376", "data": "450", "fix": "451", "desc": "385"}, {"messageId": "376", "data": "452", "fix": "453", "desc": "388"}, {"desc": "454", "fix": "455"}, {"messageId": "392", "fix": "456", "desc": "394"}, {"messageId": "395", "fix": "457", "desc": "397"}, {"messageId": "376", "data": "458", "fix": "459", "desc": "379"}, {"messageId": "376", "data": "460", "fix": "461", "desc": "382"}, {"messageId": "376", "data": "462", "fix": "463", "desc": "385"}, {"messageId": "376", "data": "464", "fix": "465", "desc": "388"}, {"messageId": "376", "data": "466", "fix": "467", "desc": "379"}, {"messageId": "376", "data": "468", "fix": "469", "desc": "382"}, {"messageId": "376", "data": "470", "fix": "471", "desc": "385"}, {"messageId": "376", "data": "472", "fix": "473", "desc": "388"}, {"messageId": "376", "data": "474", "fix": "475", "desc": "379"}, {"messageId": "376", "data": "476", "fix": "477", "desc": "382"}, {"messageId": "376", "data": "478", "fix": "479", "desc": "385"}, {"messageId": "376", "data": "480", "fix": "481", "desc": "388"}, {"messageId": "392", "fix": "482", "desc": "394"}, {"messageId": "395", "fix": "483", "desc": "397"}, {"messageId": "392", "fix": "484", "desc": "394"}, {"messageId": "395", "fix": "485", "desc": "397"}, "replaceWithAlt", {"alt": "486"}, {"range": "487", "text": "488"}, "Replace with `&apos;`.", {"alt": "489"}, {"range": "490", "text": "491"}, "Replace with `&lsquo;`.", {"alt": "492"}, {"range": "493", "text": "494"}, "Replace with `&#39;`.", {"alt": "495"}, {"range": "496", "text": "497"}, "Replace with `&rsquo;`.", "replaceEmptyInterfaceWithSuper", {"range": "498", "text": "499"}, "Replace empty interface with a type alias.", "suggestUnknown", {"range": "500", "text": "501"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "502", "text": "503"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "504", "text": "501"}, {"range": "505", "text": "503"}, {"range": "506", "text": "501"}, {"range": "507", "text": "503"}, {"range": "508", "text": "501"}, {"range": "509", "text": "503"}, {"range": "510", "text": "501"}, {"range": "511", "text": "503"}, {"range": "512", "text": "501"}, {"range": "513", "text": "503"}, {"range": "514", "text": "501"}, {"range": "515", "text": "503"}, {"range": "516", "text": "501"}, {"range": "517", "text": "503"}, {"range": "518", "text": "501"}, {"range": "519", "text": "503"}, {"range": "520", "text": "501"}, {"range": "521", "text": "503"}, {"range": "522", "text": "501"}, {"range": "523", "text": "503"}, {"range": "524", "text": "501"}, {"range": "525", "text": "503"}, {"range": "526", "text": "501"}, {"range": "527", "text": "503"}, {"alt": "486"}, {"range": "528", "text": "529"}, {"alt": "489"}, {"range": "530", "text": "531"}, {"alt": "492"}, {"range": "532", "text": "533"}, {"alt": "495"}, {"range": "534", "text": "535"}, {"alt": "486"}, {"range": "536", "text": "537"}, {"alt": "489"}, {"range": "538", "text": "539"}, {"alt": "492"}, {"range": "540", "text": "541"}, {"alt": "495"}, {"range": "542", "text": "543"}, {"alt": "486"}, {"range": "544", "text": "545"}, {"alt": "489"}, {"range": "546", "text": "547"}, {"alt": "492"}, {"range": "548", "text": "549"}, {"alt": "495"}, {"range": "550", "text": "551"}, {"alt": "486"}, {"range": "552", "text": "553"}, {"alt": "489"}, {"range": "554", "text": "555"}, {"alt": "492"}, {"range": "556", "text": "557"}, {"alt": "495"}, {"range": "558", "text": "559"}, "Update the dependencies array to be: [fetchPayments, pagination.page, statusFilter]", {"range": "560", "text": "561"}, {"range": "562", "text": "501"}, {"range": "563", "text": "503"}, {"alt": "486"}, {"range": "564", "text": "565"}, {"alt": "489"}, {"range": "566", "text": "567"}, {"alt": "492"}, {"range": "568", "text": "569"}, {"alt": "495"}, {"range": "570", "text": "571"}, {"alt": "486"}, {"range": "572", "text": "573"}, {"alt": "489"}, {"range": "574", "text": "575"}, {"alt": "492"}, {"range": "576", "text": "577"}, {"alt": "495"}, {"range": "578", "text": "579"}, {"alt": "486"}, {"range": "580", "text": "581"}, {"alt": "489"}, {"range": "582", "text": "583"}, {"alt": "492"}, {"range": "584", "text": "585"}, {"alt": "495"}, {"range": "586", "text": "587"}, {"range": "588", "text": "501"}, {"range": "589", "text": "503"}, {"range": "590", "text": "501"}, {"range": "591", "text": "503"}, "&apos;", [4750, 4800], "\n                  You&apos;re offline\n                ", "&lsquo;", [4750, 4800], "\n                  You&lsquo;re offline\n                ", "&#39;", [4750, 4800], "\n                  You&#39;re offline\n                ", "&rsquo;", [4750, 4800], "\n                  You&rsquo;re offline\n                ", [75, 152], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [1756, 1759], "unknown", [1756, 1759], "never", [726, 729], [726, 729], [1090, 1093], [1090, 1093], [1743, 1746], [1743, 1746], [5299, 5302], [5299, 5302], [1989, 1992], [1989, 1992], [3488, 3491], [3488, 3491], [1091, 1094], [1091, 1094], [881, 884], [881, 884], [1360, 1363], [1360, 1363], [1460, 1463], [1460, 1463], [1559, 1562], [1559, 1562], [1071, 1074], [1071, 1074], [11674, 11763], "\n                Assessments assigned to you that haven&apos;t been started yet\n              ", [11674, 11763], "\n                Assessments assigned to you that haven&lsquo;t been started yet\n              ", [11674, 11763], "\n                Assessments assigned to you that haven&#39;t been started yet\n              ", [11674, 11763], "\n                Assessments assigned to you that haven&rsquo;t been started yet\n              ", [13675, 13759], "\n                Assessments you&apos;ve started but haven't completed yet\n              ", [13675, 13759], "\n                Assessments you&lsquo;ve started but haven't completed yet\n              ", [13675, 13759], "\n                Assessments you&#39;ve started but haven't completed yet\n              ", [13675, 13759], "\n                Assessments you&rsquo;ve started but haven't completed yet\n              ", [13675, 13759], "\n                Assessments you've started but haven&apos;t completed yet\n              ", [13675, 13759], "\n                Assessments you've started but haven&lsquo;t completed yet\n              ", [13675, 13759], "\n                Assessments you've started but haven&#39;t completed yet\n              ", [13675, 13759], "\n                Assessments you've started but haven&rsquo;t completed yet\n              ", [3119, 3168], ", here&apos;s your learning progress today.\n          ", [3119, 3168], ", here&lsquo;s your learning progress today.\n          ", [3119, 3168], ", here&#39;s your learning progress today.\n          ", [3119, 3168], ", here&rsquo;s your learning progress today.\n          ", [2433, 2464], "[fetchPayments, pagination.page, statusFilter]", [944, 947], [944, 947], [2971, 3023], "You don&apos;t have permission to manage synchronization.", [2971, 3023], "You don&lsquo;t have permission to manage synchronization.", [2971, 3023], "You don&#39;t have permission to manage synchronization.", [2971, 3023], "You don&rsquo;t have permission to manage synchronization.", [1445, 1483], "\n            You&apos;re Offline\n          ", [1445, 1483], "\n            You&lsquo;re Offline\n          ", [1445, 1483], "\n            You&#39;re Offline\n          ", [1445, 1483], "\n            You&rsquo;re Offline\n          ", [1544, 1663], "\n            It looks like you&apos;ve lost your internet connection. Some features may be limited while offline.\n          ", [1544, 1663], "\n            It looks like you&lsquo;ve lost your internet connection. Some features may be limited while offline.\n          ", [1544, 1663], "\n            It looks like you&#39;ve lost your internet connection. Some features may be limited while offline.\n          ", [1544, 1663], "\n            It looks like you&rsquo;ve lost your internet connection. Some features may be limited while offline.\n          ", [630, 633], [630, 633], [11558, 11561], [11558, 11561]]