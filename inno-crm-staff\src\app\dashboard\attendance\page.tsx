"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { 
  Plus, 
  Search, 
  Edit, 
  Eye, 
  Calendar, 
  Users, 
  CheckCircle, 
  XCircle, 
  Clock,
  AlertTriangle,
  BarChart3
} from "lucide-react"
import DashboardLayout from "@/components/dashboard/layout"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"

const createAttendanceSchema = z.object({
  groupId: z.string().min(1, "Group is required"),
  date: z.string().min(1, "Date is required"),
  lessonTopic: z.string().optional(),
  homework: z.string().optional(),
  notes: z.string().optional(),
})

type CreateAttendanceForm = z.infer<typeof createAttendanceSchema>

interface AttendanceRecord {
  id: string
  date: string
  lessonTopic?: string
  homework?: string
  notes?: string
  group: {
    id: string
    name: string
    course: {
      name: string
    }
    teacher: {
      user: {
        name: string
      }
    }
  }
  studentAttendances: Array<{
    id: string
    status: string
    notes?: string
    studentReference: {
      id: string
      name: string
    }
  }>
  takenByUser?: {
    name: string
  }
}

interface Group {
  id: string
  name: string
  course: {
    name: string
  }
  teacher: {
    user: {
      name: string
    }
  }
}

interface Student {
  id: string
  name: string
}

const statusLabels = {
  PRESENT: "Present",
  ABSENT: "Absent", 
  LATE: "Late",
  EXCUSED: "Excused"
}

const statusColors = {
  PRESENT: "bg-green-100 text-green-800",
  ABSENT: "bg-red-100 text-red-800",
  LATE: "bg-yellow-100 text-yellow-800",
  EXCUSED: "bg-blue-100 text-blue-800"
}

const statusIcons = {
  PRESENT: <CheckCircle className="h-4 w-4 text-green-600" />,
  ABSENT: <XCircle className="h-4 w-4 text-red-600" />,
  LATE: <Clock className="h-4 w-4 text-yellow-600" />,
  EXCUSED: <AlertTriangle className="h-4 w-4 text-blue-600" />
}

export default function AttendanceManagementPage() {
  const { data: session } = useSession()
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([])
  const [groups, setGroups] = useState<Group[]>([])
  const [students, setStudents] = useState<Student[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [groupFilter, setGroupFilter] = useState("")
  const [dateFromFilter, setDateFromFilter] = useState("")
  const [dateToFilter, setDateToFilter] = useState("")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null)
  const [studentAttendances, setStudentAttendances] = useState<Record<string, string>>({})
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  })

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
    watch
  } = useForm<CreateAttendanceForm>({
    resolver: zodResolver(createAttendanceSchema),
  })

  const watchedGroupId = watch("groupId")

  const fetchAttendanceRecords = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(groupFilter && { groupId: groupFilter }),
        ...(dateFromFilter && { dateFrom: dateFromFilter }),
        ...(dateToFilter && { dateTo: dateToFilter })
      })

      const response = await fetch(`/api/attendance?${params}`)
      if (response.ok) {
        const data = await response.json()
        setAttendanceRecords(data.attendanceRecords)
        setPagination(data.pagination)
      }
    } catch (error) {
      console.error("Error fetching attendance records:", error)
    } finally {
      setLoading(false)
    }
  }

  const fetchGroups = async () => {
    try {
      const response = await fetch("/api/groups?limit=100&isActive=true")
      if (response.ok) {
        const data = await response.json()
        setGroups(data.groups)
      }
    } catch (error) {
      console.error("Error fetching groups:", error)
    }
  }

  const fetchStudentsForGroup = async (groupId: string) => {
    try {
      const response = await fetch(`/api/students?limit=100&groupId=${groupId}`)
      if (response.ok) {
        const data = await response.json()
        setStudents(data.students)
        
        // Initialize attendance status for all students
        const initialAttendances: Record<string, string> = {}
        data.students.forEach((student: Student) => {
          initialAttendances[student.id] = "PRESENT"
        })
        setStudentAttendances(initialAttendances)
      }
    } catch (error) {
      console.error("Error fetching students:", error)
    }
  }

  useEffect(() => {
    fetchAttendanceRecords()
  }, [pagination.page, groupFilter, dateFromFilter, dateToFilter])

  useEffect(() => {
    fetchGroups()
  }, [])

  useEffect(() => {
    if (watchedGroupId) {
      const group = groups.find(g => g.id === watchedGroupId)
      setSelectedGroup(group || null)
      fetchStudentsForGroup(watchedGroupId)
    }
  }, [watchedGroupId, groups])

  const onSubmit = async (data: CreateAttendanceForm) => {
    try {
      const studentAttendanceArray = Object.entries(studentAttendances).map(([studentId, status]) => ({
        studentId,
        status,
        notes: ""
      }))

      const response = await fetch("/api/attendance", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          studentAttendances: studentAttendanceArray
        }),
      })

      if (response.ok) {
        setIsCreateDialogOpen(false)
        reset()
        setStudentAttendances({})
        setSelectedGroup(null)
        setStudents([])
        fetchAttendanceRecords()
      } else {
        const error = await response.json()
        console.error("Error creating attendance:", error)
      }
    } catch (error) {
      console.error("Error creating attendance:", error)
    }
  }

  const updateStudentAttendance = (studentId: string, status: string) => {
    setStudentAttendances(prev => ({
      ...prev,
      [studentId]: status
    }))
  }

  const getAttendanceSummary = (record: AttendanceRecord) => {
    const summary = record.studentAttendances.reduce((acc, attendance) => {
      acc[attendance.status] = (acc[attendance.status] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return summary
  }

  const canTakeAttendance = session?.user?.role && ["ADMIN", "MANAGER", "TEACHER", "ACADEMIC_MANAGER"].includes(session.user.role)

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Attendance Management</h1>
            <p className="text-gray-600">Track and manage student attendance records</p>
          </div>
          
          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              <BarChart3 className="h-4 w-4 mr-2" />
              Analytics
            </Button>
            
            {canTakeAttendance && (
              <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Take Attendance
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Take Attendance</DialogTitle>
                    <DialogDescription>
                      Record attendance for a class session.
                    </DialogDescription>
                  </DialogHeader>
                  <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="groupId">Group</Label>
                        <Select onValueChange={(value) => setValue("groupId", value)}>
                          <SelectTrigger className={errors.groupId ? "border-red-500" : ""}>
                            <SelectValue placeholder="Select group" />
                          </SelectTrigger>
                          <SelectContent>
                            {groups.map((group) => (
                              <SelectItem key={group.id} value={group.id}>
                                {group.name} - {group.course.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {errors.groupId && (
                          <p className="text-sm text-red-500 mt-1">{errors.groupId.message}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="date">Date</Label>
                        <Input
                          id="date"
                          type="date"
                          defaultValue={new Date().toISOString().split('T')[0]}
                          {...register("date")}
                          className={errors.date ? "border-red-500" : ""}
                        />
                        {errors.date && (
                          <p className="text-sm text-red-500 mt-1">{errors.date.message}</p>
                        )}
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="lessonTopic">Lesson Topic (Optional)</Label>
                      <Input
                        id="lessonTopic"
                        placeholder="What was covered in this lesson?"
                        {...register("lessonTopic")}
                      />
                    </div>

                    <div>
                      <Label htmlFor="homework">Homework (Optional)</Label>
                      <Input
                        id="homework"
                        placeholder="Homework assignments given"
                        {...register("homework")}
                      />
                    </div>

                    <div>
                      <Label htmlFor="notes">Notes (Optional)</Label>
                      <Input
                        id="notes"
                        placeholder="Additional notes about the session"
                        {...register("notes")}
                      />
                    </div>

                    {/* Student Attendance */}
                    {selectedGroup && students.length > 0 && (
                      <div>
                        <Label>Student Attendance</Label>
                        <div className="border rounded-lg p-4 max-h-60 overflow-y-auto">
                          <div className="space-y-3">
                            {students.map((student) => (
                              <div key={student.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                                <span className="font-medium">{student.name}</span>
                                <Select 
                                  value={studentAttendances[student.id] || "PRESENT"}
                                  onValueChange={(value) => updateStudentAttendance(student.id, value)}
                                >
                                  <SelectTrigger className="w-32">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {Object.entries(statusLabels).map(([value, label]) => (
                                      <SelectItem key={value} value={value}>
                                        {label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="flex justify-end space-x-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          setIsCreateDialogOpen(false)
                          reset()
                          setStudentAttendances({})
                          setSelectedGroup(null)
                          setStudents([])
                        }}
                      >
                        Cancel
                      </Button>
                      <Button type="submit" disabled={isSubmitting || !selectedGroup}>
                        {isSubmitting ? "Recording..." : "Record Attendance"}
                      </Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>
            )}
          </div>
        </div>

        {/* Attendance Records */}
        <Card>
          <CardHeader>
            <CardTitle>Attendance Records</CardTitle>
            <CardDescription>
              View and manage all attendance records
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4 mb-6">
              <div className="flex-1 min-w-64">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search attendance records..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={groupFilter} onValueChange={setGroupFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Filter by group" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Groups</SelectItem>
                  {groups.map((group) => (
                    <SelectItem key={group.id} value={group.id}>
                      {group.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Input
                type="date"
                placeholder="From date"
                value={dateFromFilter}
                onChange={(e) => setDateFromFilter(e.target.value)}
                className="w-40"
              />
              <Input
                type="date"
                placeholder="To date"
                value={dateToFilter}
                onChange={(e) => setDateToFilter(e.target.value)}
                className="w-40"
              />
            </div>

            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Group</TableHead>
                    <TableHead>Lesson Topic</TableHead>
                    <TableHead>Attendance Summary</TableHead>
                    <TableHead>Taken By</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        Loading...
                      </TableCell>
                    </TableRow>
                  ) : attendanceRecords.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        No attendance records found
                      </TableCell>
                    </TableRow>
                  ) : (
                    attendanceRecords.map((record) => {
                      const summary = getAttendanceSummary(record)
                      return (
                        <TableRow key={record.id}>
                          <TableCell>
                            <div className="flex items-center">
                              <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                              {new Date(record.date).toLocaleDateString()}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{record.group.name}</div>
                              <div className="text-sm text-gray-500">{record.group.course.name}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="max-w-48 truncate">
                              {record.lessonTopic || "-"}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <div className="flex items-center">
                                <CheckCircle className="h-3 w-3 text-green-600 mr-1" />
                                <span className="text-sm">{summary.PRESENT || 0}</span>
                              </div>
                              <div className="flex items-center">
                                <XCircle className="h-3 w-3 text-red-600 mr-1" />
                                <span className="text-sm">{summary.ABSENT || 0}</span>
                              </div>
                              <div className="flex items-center">
                                <Clock className="h-3 w-3 text-yellow-600 mr-1" />
                                <span className="text-sm">{summary.LATE || 0}</span>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className="text-sm text-gray-600">
                              {record.takenByUser?.name || "Unknown"}
                            </span>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Button variant="ghost" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                              {canTakeAttendance && (
                                <Button variant="ghost" size="sm">
                                  <Edit className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            {pagination.pages > 1 && (
              <div className="flex items-center justify-between mt-4">
                <p className="text-sm text-gray-700">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to{" "}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
                  {pagination.total} results
                </p>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                    disabled={pagination.page === 1}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                    disabled={pagination.page === pagination.pages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
