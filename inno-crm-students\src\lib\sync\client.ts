import { prisma } from "@/lib/database/prisma"

interface SyncConfig {
  staffServerUrl: string
  apiKey: string
  retryAttempts: number
  retryDelay: number
}

interface SyncResult {
  success: boolean
  syncedCount: number
  errors: string[]
  lastSyncTime: string
}

class SyncClient {
  private config: SyncConfig

  constructor() {
    this.config = {
      staffServerUrl: process.env.STAFF_SERVER_URL || "http://localhost:3001",
      apiKey: process.env.INTER_SERVER_API_KEY || "",
      retryAttempts: 3,
      retryDelay: 1000
    }
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
    const url = `${this.config.staffServerUrl}/api/sync${endpoint}`
    
    for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
      try {
        const response = await fetch(url, {
          ...options,
          headers: {
            'Content-Type': 'application/json',
            ...options.headers
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        return await response.json()
      } catch (error) {
        console.error(`Sync attempt ${attempt} failed:`, error)
        
        if (attempt === this.config.retryAttempts) {
          throw error
        }
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * attempt))
      }
    }
  }

  async syncStudentData(studentId?: string): Promise<SyncResult> {
    try {
      // Get last sync time from database
      const lastSync = await prisma.syncLog.findFirst({
        where: { resource: "students" },
        orderBy: { createdAt: "desc" }
      })

      const requestBody = {
        apiKey: this.config.apiKey,
        ...(lastSync && { lastSyncTime: lastSync.lastSyncTime.toISOString() }),
        ...(studentId && { studentIds: [studentId] })
      }

      const response = await this.makeRequest("/students", {
        method: "POST",
        body: JSON.stringify(requestBody)
      })

      if (!response.success) {
        throw new Error("Sync request failed")
      }

      let syncedCount = 0
      const errors: string[] = []

      // Process each student
      for (const studentData of response.data) {
        try {
          // Upsert student
          await prisma.student.upsert({
            where: { staffReferenceId: studentData.id },
            update: {
              name: studentData.name,
              phone: studentData.phone,
              level: studentData.level,
              branch: studentData.branch,
              status: studentData.status,
              emergencyContact: studentData.emergencyContact,
              lastSyncedAt: new Date(studentData.lastSyncedAt || studentData.updatedAt)
            },
            create: {
              staffReferenceId: studentData.id,
              name: studentData.name,
              phone: studentData.phone,
              level: studentData.level,
              branch: studentData.branch,
              status: studentData.status,
              emergencyContact: studentData.emergencyContact,
              lastSyncedAt: new Date(studentData.lastSyncedAt || studentData.updatedAt)
            }
          })

          // Sync group reference if exists
          if (studentData.currentGroup) {
            await prisma.groupReference.upsert({
              where: { staffGroupId: studentData.currentGroup.id },
              update: {
                name: studentData.currentGroup.name,
                schedule: studentData.currentGroup.schedule,
                room: studentData.currentGroup.room,
                branch: studentData.currentGroup.branch,
                startDate: new Date(studentData.currentGroup.startDate),
                endDate: new Date(studentData.currentGroup.endDate),
                isActive: studentData.currentGroup.isActive
              },
              create: {
                staffGroupId: studentData.currentGroup.id,
                name: studentData.currentGroup.name,
                schedule: studentData.currentGroup.schedule,
                room: studentData.currentGroup.room,
                branch: studentData.currentGroup.branch,
                startDate: new Date(studentData.currentGroup.startDate),
                endDate: new Date(studentData.currentGroup.endDate),
                isActive: studentData.currentGroup.isActive
              }
            })

            // Sync teacher reference if exists
            if (studentData.currentGroup.teacher) {
              await prisma.teacherReference.upsert({
                where: { staffTeacherId: studentData.currentGroup.teacher.id },
                update: {
                  name: studentData.currentGroup.teacher.user.name,
                  phone: studentData.currentGroup.teacher.user.phone,
                  email: studentData.currentGroup.teacher.user.email,
                  subject: studentData.currentGroup.teacher.subject,
                  tier: studentData.currentGroup.teacher.tier
                },
                create: {
                  staffTeacherId: studentData.currentGroup.teacher.id,
                  staffUserId: studentData.currentGroup.teacher.user.id,
                  name: studentData.currentGroup.teacher.user.name,
                  phone: studentData.currentGroup.teacher.user.phone,
                  email: studentData.currentGroup.teacher.user.email,
                  subject: studentData.currentGroup.teacher.subject,
                  tier: studentData.currentGroup.teacher.tier
                }
              })
            }
          }

          // Sync payments
          for (const paymentData of studentData.payments) {
            await prisma.payment.upsert({
              where: { staffPaymentId: paymentData.id },
              update: {
                amount: paymentData.amount,
                method: paymentData.method,
                status: paymentData.status,
                description: paymentData.description,
                transactionId: paymentData.transactionId,
                dueDate: paymentData.dueDate ? new Date(paymentData.dueDate) : null,
                paidDate: paymentData.paidDate ? new Date(paymentData.paidDate) : null
              },
              create: {
                staffPaymentId: paymentData.id,
                studentId: (await prisma.student.findUnique({
                  where: { staffReferenceId: studentData.id }
                }))!.id,
                amount: paymentData.amount,
                method: paymentData.method,
                status: paymentData.status,
                description: paymentData.description,
                transactionId: paymentData.transactionId,
                dueDate: paymentData.dueDate ? new Date(paymentData.dueDate) : null,
                paidDate: paymentData.paidDate ? new Date(paymentData.paidDate) : null
              }
            })
          }

          syncedCount++
        } catch (error) {
          console.error(`Error syncing student ${studentData.id}:`, error)
          errors.push(`Student ${studentData.name}: ${error}`)
        }
      }

      // Log successful sync
      await prisma.syncLog.create({
        data: {
          resource: "students",
          action: "SYNC",
          recordCount: syncedCount,
          lastSyncTime: new Date(response.syncTime),
          success: errors.length === 0,
          errors: errors.length > 0 ? errors : null
        }
      })

      return {
        success: errors.length === 0,
        syncedCount,
        errors,
        lastSyncTime: response.syncTime
      }
    } catch (error) {
      console.error("Student sync failed:", error)
      
      // Log failed sync
      await prisma.syncLog.create({
        data: {
          resource: "students",
          action: "SYNC",
          recordCount: 0,
          lastSyncTime: new Date(),
          success: false,
          errors: [String(error)]
        }
      })

      return {
        success: false,
        syncedCount: 0,
        errors: [String(error)],
        lastSyncTime: new Date().toISOString()
      }
    }
  }

  async syncAnnouncements(): Promise<SyncResult> {
    try {
      // Get last sync time for announcements
      const lastSync = await prisma.syncLog.findFirst({
        where: { resource: "announcements" },
        orderBy: { createdAt: "desc" }
      })

      const url = `/announcements?apiKey=${this.config.apiKey}${
        lastSync ? `&lastSyncTime=${lastSync.lastSyncTime.toISOString()}` : ""
      }`

      const response = await this.makeRequest(url)

      if (!response.success) {
        throw new Error("Announcement sync request failed")
      }

      let syncedCount = 0
      const errors: string[] = []

      // Process each announcement
      for (const announcementData of response.data) {
        try {
          await prisma.announcement.upsert({
            where: { staffAnnouncementId: announcementData.id },
            update: {
              title: announcementData.title,
              content: announcementData.content,
              type: announcementData.type,
              targetAudience: announcementData.targetAudience,
              priority: announcementData.priority,
              isActive: announcementData.isActive,
              expiresAt: announcementData.expiresAt ? new Date(announcementData.expiresAt) : null,
              authorName: announcementData.author.name,
              authorRole: announcementData.author.role
            },
            create: {
              staffAnnouncementId: announcementData.id,
              title: announcementData.title,
              content: announcementData.content,
              type: announcementData.type,
              targetAudience: announcementData.targetAudience,
              priority: announcementData.priority,
              isActive: announcementData.isActive,
              expiresAt: announcementData.expiresAt ? new Date(announcementData.expiresAt) : null,
              authorName: announcementData.author.name,
              authorRole: announcementData.author.role,
              createdAt: new Date(announcementData.createdAt)
            }
          })

          syncedCount++
        } catch (error) {
          console.error(`Error syncing announcement ${announcementData.id}:`, error)
          errors.push(`Announcement ${announcementData.title}: ${error}`)
        }
      }

      // Log successful sync
      await prisma.syncLog.create({
        data: {
          resource: "announcements",
          action: "SYNC",
          recordCount: syncedCount,
          lastSyncTime: new Date(response.syncTime),
          success: errors.length === 0,
          errors: errors.length > 0 ? errors : null
        }
      })

      return {
        success: errors.length === 0,
        syncedCount,
        errors,
        lastSyncTime: response.syncTime
      }
    } catch (error) {
      console.error("Announcement sync failed:", error)
      
      // Log failed sync
      await prisma.syncLog.create({
        data: {
          resource: "announcements",
          action: "SYNC",
          recordCount: 0,
          lastSyncTime: new Date(),
          success: false,
          errors: [String(error)]
        }
      })

      return {
        success: false,
        syncedCount: 0,
        errors: [String(error)],
        lastSyncTime: new Date().toISOString()
      }
    }
  }

  async getSyncStatus(): Promise<any> {
    try {
      const url = `/students?apiKey=${this.config.apiKey}`
      return await this.makeRequest(url, { method: "GET" })
    } catch (error) {
      console.error("Error getting sync status:", error)
      throw error
    }
  }
}

export const syncClient = new SyncClient()
