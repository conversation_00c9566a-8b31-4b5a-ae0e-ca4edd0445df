{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/auth|_next\\/static|_next\\/image|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "AHx_O0a9dBXHcPi27Y3xo", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a4r8t03fi4pQSqki3XHKEUbOWBFF3ZwHxl5kHPefQZI=", "__NEXT_PREVIEW_MODE_ID": "e36ae3cff08cfaba5023493ccb07d5d5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f4642b7c8e661f198656e38337688d2d94b8160a90b0d0a772f614fddd254d9d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "27fefe64bcd3e570ded7c30bffef769240929723ef662d6e8fee5d2328d1e148"}}}, "functions": {}, "sortedMiddleware": ["/"]}