{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/auth|_next\\/static|_next\\/image|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "MO2mnBESL9P--nfAVWbNL", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a4r8t03fi4pQSqki3XHKEUbOWBFF3ZwHxl5kHPefQZI=", "__NEXT_PREVIEW_MODE_ID": "39be337835733cfe1e5aaed818b22e90", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fed6014982fe47bc97dd105770e69f780dd933ee872c03b4e408b839b0fdcce2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "dc37422c3d7d6b42c43db1939593672f8e19c5618d45facec4fee246d2a9a9c9"}}}, "functions": {}, "sortedMiddleware": ["/"]}