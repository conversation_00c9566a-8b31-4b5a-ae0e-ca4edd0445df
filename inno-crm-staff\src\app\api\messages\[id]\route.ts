import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const updateMessageSchema = z.object({
  subject: z.string().min(1, "Subject is required").optional(),
  content: z.string().min(1, "Content is required").optional(),
  recipientType: z.enum(["ALL", "STUDENTS", "TEACHERS", "ACADEMIC_MANAGERS", "SPECIFIC"]).optional(),
  recipientIds: z.array(z.string()).optional(),
  priority: z.enum(["LOW", "MEDIUM", "HIGH"]).optional(),
  status: z.enum(["DRAFT", "SENT", "FAILED"]).optional(),
})

const sendMessageSchema = z.object({
  action: z.literal("send"),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const message = await prisma.message.findUnique({
      where: { id: params.id },
      include: {
        sender: {
          select: { id: true, name: true, role: true }
        }
      }
    })

    if (!message) {
      return NextResponse.json({ error: "Message not found" }, { status: 404 })
    }

    // Check if user has permission to view this message
    if (!["ADMIN", "MANAGER"].includes(session.user.role) && message.senderId !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
    }

    return NextResponse.json(message)
  } catch (error) {
    console.error("Error fetching message:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "TEACHER", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    
    // Check if this is a send action
    if (body.action === "send") {
      const message = await prisma.message.findUnique({
        where: { id: params.id }
      })

      if (!message) {
        return NextResponse.json({ error: "Message not found" }, { status: 404 })
      }

      // Check if user owns this message or is admin/manager
      if (!["ADMIN", "MANAGER"].includes(session.user.role) && message.senderId !== session.user.id) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
      }

      if (message.status !== "DRAFT") {
        return NextResponse.json({ error: "Only draft messages can be sent" }, { status: 400 })
      }

      // Update message status to sent
      const updatedMessage = await prisma.message.update({
        where: { id: params.id },
        data: {
          status: "SENT",
          sentAt: new Date()
        },
        include: {
          sender: {
            select: { id: true, name: true, role: true }
          }
        }
      })

      // If recipients include students, broadcast to students server
      if (message.recipientType === "STUDENTS" || message.recipientType === "ALL") {
        try {
          const broadcastData = {
            messageId: message.id,
            subject: message.subject,
            content: message.content,
            recipientType: message.recipientType,
            recipientIds: message.recipientIds,
            priority: message.priority,
            senderId: session.user.id,
            senderName: session.user.name || "Staff Member"
          }

          const response = await fetch("/api/inter-server/messages/broadcast", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(broadcastData),
          })

          if (!response.ok) {
            throw new Error("Failed to broadcast message")
          }

          console.log("Message broadcasted to students server:", message.id)
        } catch (error) {
          console.error("Error broadcasting message to students:", error)
          // Update message status to failed
          await prisma.message.update({
            where: { id: params.id },
            data: { status: "FAILED" }
          })
        }
      }

      return NextResponse.json(updatedMessage)
    } else {
      // Regular update
      const validatedData = updateMessageSchema.parse(body)

      const message = await prisma.message.findUnique({
        where: { id: params.id }
      })

      if (!message) {
        return NextResponse.json({ error: "Message not found" }, { status: 404 })
      }

      // Check if user owns this message or is admin/manager
      if (!["ADMIN", "MANAGER"].includes(session.user.role) && message.senderId !== session.user.id) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
      }

      // Only allow editing draft messages
      if (message.status !== "DRAFT") {
        return NextResponse.json({ error: "Only draft messages can be edited" }, { status: 400 })
      }

      const updatedMessage = await prisma.message.update({
        where: { id: params.id },
        data: validatedData,
        include: {
          sender: {
            select: { id: true, name: true, role: true }
          }
        }
      })

      return NextResponse.json(updatedMessage)
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error updating message:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "TEACHER", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const message = await prisma.message.findUnique({
      where: { id: params.id }
    })

    if (!message) {
      return NextResponse.json({ error: "Message not found" }, { status: 404 })
    }

    // Check if user owns this message or is admin/manager
    if (!["ADMIN", "MANAGER"].includes(session.user.role) && message.senderId !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
    }

    // Only allow deleting draft messages
    if (message.status !== "DRAFT") {
      return NextResponse.json({ error: "Only draft messages can be deleted" }, { status: 400 })
    }

    await prisma.message.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: "Message deleted successfully" })
  } catch (error) {
    console.error("Error deleting message:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
