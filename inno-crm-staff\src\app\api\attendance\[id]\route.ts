import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const updateAttendanceSchema = z.object({
  studentAttendances: z.array(z.object({
    id: z.string().optional(),
    studentId: z.string(),
    status: z.enum(["PRESENT", "ABSENT", "LATE", "EXCUSED"]),
    notes: z.string().optional()
  })).optional(),
  lessonTopic: z.string().optional(),
  homework: z.string().optional(),
  notes: z.string().optional()
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const attendanceRecord = await prisma.attendanceRecord.findUnique({
      where: { id: params.id },
      include: {
        group: {
          include: {
            course: true,
            teacher: {
              include: {
                user: true
              }
            }
          }
        },
        studentAttendances: {
          include: {
            studentReference: true
          }
        },
        takenByUser: {
          select: {
            id: true,
            name: true,
            role: true
          }
        }
      }
    })

    if (!attendanceRecord) {
      return NextResponse.json({ error: "Attendance record not found" }, { status: 404 })
    }

    return NextResponse.json(attendanceRecord)
  } catch (error) {
    console.error("Error fetching attendance record:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "TEACHER", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updateAttendanceSchema.parse(body)

    // Check if attendance record exists
    const existingRecord = await prisma.attendanceRecord.findUnique({
      where: { id: params.id },
      include: {
        group: {
          include: {
            teacher: {
              include: {
                user: true
              }
            }
          }
        }
      }
    })

    if (!existingRecord) {
      return NextResponse.json({ error: "Attendance record not found" }, { status: 404 })
    }

    // Check permissions
    if (session.user.role === "TEACHER" && existingRecord.group.teacher.user.id !== session.user.id) {
      return NextResponse.json({ error: "You can only edit attendance for your own groups" }, { status: 403 })
    }

    // Update attendance record
    const updatedRecord = await prisma.attendanceRecord.update({
      where: { id: params.id },
      data: {
        lessonTopic: validatedData.lessonTopic,
        homework: validatedData.homework,
        notes: validatedData.notes
      }
    })

    // Update student attendances if provided
    if (validatedData.studentAttendances) {
      // Delete existing student attendances
      await prisma.studentAttendance.deleteMany({
        where: { attendanceRecordId: params.id }
      })

      // Create new student attendances
      await prisma.studentAttendance.createMany({
        data: validatedData.studentAttendances.map(attendance => ({
          attendanceRecordId: params.id,
          studentReferenceId: attendance.studentId,
          status: attendance.status,
          notes: attendance.notes
        }))
      })
    }

    // Get updated record with relations
    const finalRecord = await prisma.attendanceRecord.findUnique({
      where: { id: params.id },
      include: {
        group: {
          include: {
            course: true,
            teacher: {
              include: {
                user: true
              }
            }
          }
        },
        studentAttendances: {
          include: {
            studentReference: true
          }
        }
      }
    })

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: session.user.id,
        userRole: session.user.role as any,
        action: "UPDATE",
        resource: "attendance",
        resourceId: params.id,
        details: {
          groupName: existingRecord.group.name,
          date: existingRecord.date,
          changes: validatedData
        }
      }
    })

    return NextResponse.json(finalRecord)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error updating attendance record:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check if attendance record exists
    const existingRecord = await prisma.attendanceRecord.findUnique({
      where: { id: params.id },
      include: {
        group: true
      }
    })

    if (!existingRecord) {
      return NextResponse.json({ error: "Attendance record not found" }, { status: 404 })
    }

    // Delete attendance record (cascade will handle student attendances)
    await prisma.attendanceRecord.delete({
      where: { id: params.id }
    })

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: session.user.id,
        userRole: session.user.role as any,
        action: "DELETE",
        resource: "attendance",
        resourceId: params.id,
        details: {
          groupName: existingRecord.group.name,
          date: existingRecord.date
        }
      }
    })

    return NextResponse.json({ message: "Attendance record deleted successfully" })
  } catch (error) {
    console.error("Error deleting attendance record:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
