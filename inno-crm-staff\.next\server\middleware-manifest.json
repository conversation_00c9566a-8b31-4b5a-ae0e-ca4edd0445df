{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/auth|_next\\/static|_next\\/image|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "hZ9J63HhMWWMl5ho87faF", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pIn14xo1WIqISjdZ5hTtkoZ1Qf8NMBPwIRtsMoJ+vjY=", "__NEXT_PREVIEW_MODE_ID": "d78c858b8c6041c08a3c2da9fafde350", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a51bfc97d0538d1fab595f93367767c0a76ee886e3d7b0c9d4cda95572d3a045", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fed40dc24ad2670a7da89f0fba86ee1034120d1a141f56f6bd0094687fcee656"}}}, "functions": {}, "sortedMiddleware": ["/"]}