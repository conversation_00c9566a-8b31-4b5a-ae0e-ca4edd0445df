// Database optimization utilities for the staff portal

interface IndexRecommendation {
  table: string
  columns: string[]
  type: 'btree' | 'hash' | 'gin' | 'gist'
  reason: string
  priority: 'high' | 'medium' | 'low'
}

interface QueryOptimization {
  query: string
  optimization: string
  impact: 'high' | 'medium' | 'low'
  description: string
}

export class DatabaseOptimizer {
  
  // Get recommended indexes for better performance
  getIndexRecommendations(): IndexRecommendation[] {
    return [
      {
        table: 'student_references',
        columns: ['phone'],
        type: 'btree',
        reason: 'Frequent lookups by phone number',
        priority: 'high'
      },
      {
        table: 'student_references',
        columns: ['status', 'branch'],
        type: 'btree',
        reason: 'Common filtering in student lists',
        priority: 'high'
      },
      {
        table: 'student_references',
        columns: ['currentGroupId'],
        type: 'btree',
        reason: 'Group-based queries',
        priority: 'medium'
      },
      {
        table: 'leads',
        columns: ['status', 'branch'],
        type: 'btree',
        reason: 'Lead filtering and reporting',
        priority: 'high'
      },
      {
        table: 'leads',
        columns: ['assignedTo', 'status'],
        type: 'btree',
        reason: 'Staff assignment queries',
        priority: 'medium'
      },
      {
        table: 'groups',
        columns: ['branch', 'isActive'],
        type: 'btree',
        reason: 'Active group filtering by branch',
        priority: 'high'
      },
      {
        table: 'payment_overview',
        columns: ['studentReferenceId', 'status'],
        type: 'btree',
        reason: 'Payment status queries',
        priority: 'high'
      },
      {
        table: 'payment_overview',
        columns: ['paidDate'],
        type: 'btree',
        reason: 'Date-based payment reports',
        priority: 'medium'
      },
      {
        table: 'activity_logs',
        columns: ['userId', 'createdAt'],
        type: 'btree',
        reason: 'User activity tracking',
        priority: 'medium'
      },
      {
        table: 'activity_logs',
        columns: ['resource', 'action', 'createdAt'],
        type: 'btree',
        reason: 'Audit log queries',
        priority: 'low'
      },
      {
        table: 'call_records',
        columns: ['leadId', 'startedAt'],
        type: 'btree',
        reason: 'Call history queries',
        priority: 'medium'
      }
    ]
  }

  // Get SQL statements to create recommended indexes
  getIndexCreationSQL(): string[] {
    const recommendations = this.getIndexRecommendations()
    
    return recommendations.map(rec => {
      const indexName = `idx_${rec.table}_${rec.columns.join('_')}`
      const columns = rec.columns.join(', ')
      
      return `CREATE INDEX CONCURRENTLY IF NOT EXISTS ${indexName} ON ${rec.table} USING ${rec.type} (${columns});`
    })
  }

  // Get query optimizations
  getQueryOptimizations(): QueryOptimization[] {
    return [
      {
        query: 'SELECT * FROM student_references WHERE status = ? AND branch = ?',
        optimization: 'SELECT id, name, phone, status, branch FROM student_references WHERE status = ? AND branch = ?',
        impact: 'medium',
        description: 'Avoid SELECT * and only fetch needed columns'
      },
      {
        query: 'SELECT COUNT(*) FROM leads WHERE status IN (?, ?, ?)',
        optimization: 'Use materialized view or cached counts for dashboard statistics',
        impact: 'high',
        description: 'Cache frequently accessed counts instead of real-time calculation'
      },
      {
        query: 'Complex JOIN queries across multiple tables',
        optimization: 'Use appropriate indexes and consider query restructuring',
        impact: 'high',
        description: 'Ensure all JOIN conditions have proper indexes'
      },
      {
        query: 'Date range queries on payment_overview',
        optimization: 'Use date partitioning for large payment tables',
        impact: 'medium',
        description: 'Partition by month/year for better performance on date queries'
      }
    ]
  }

  // Database maintenance recommendations
  getMaintenanceRecommendations(): string[] {
    return [
      'Run VACUUM ANALYZE weekly to update statistics and reclaim space',
      'Monitor slow query log and optimize queries taking >1 second',
      'Set up connection pooling to manage database connections efficiently',
      'Configure appropriate work_mem and shared_buffers for your workload',
      'Enable query plan caching for frequently executed queries',
      'Set up automated backups with point-in-time recovery',
      'Monitor database size and plan for archiving old data',
      'Use read replicas for reporting queries to reduce load on primary',
      'Implement proper monitoring for connection count, CPU, and memory usage',
      'Regular security updates and access review'
    ]
  }

  // Performance monitoring queries
  getPerformanceQueries(): Record<string, string> {
    return {
      slowQueries: `
        SELECT query, calls, total_time, mean_time, rows
        FROM pg_stat_statements
        WHERE mean_time > 1000
        ORDER BY mean_time DESC
        LIMIT 10;
      `,
      
      indexUsage: `
        SELECT schemaname, tablename, attname, n_distinct, correlation
        FROM pg_stats
        WHERE schemaname = 'public'
        ORDER BY n_distinct DESC;
      `,
      
      tableSize: `
        SELECT 
          schemaname,
          tablename,
          pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
          pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
        FROM pg_tables
        WHERE schemaname = 'public'
        ORDER BY size_bytes DESC;
      `,
      
      connectionStats: `
        SELECT 
          state,
          count(*) as connections
        FROM pg_stat_activity
        WHERE datname = current_database()
        GROUP BY state;
      `,
      
      lockInfo: `
        SELECT 
          blocked_locks.pid AS blocked_pid,
          blocked_activity.usename AS blocked_user,
          blocking_locks.pid AS blocking_pid,
          blocking_activity.usename AS blocking_user,
          blocked_activity.query AS blocked_statement,
          blocking_activity.query AS current_statement_in_blocking_process
        FROM pg_catalog.pg_locks blocked_locks
        JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
        JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
        JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
        WHERE NOT blocked_locks.granted AND blocking_locks.granted;
      `
    }
  }

  // Data archival recommendations
  getArchivalStrategy(): {
    table: string
    criteria: string
    retention: string
    method: string
  }[] {
    return [
      {
        table: 'activity_logs',
        criteria: 'createdAt < NOW() - INTERVAL \'1 year\'',
        retention: '1 year',
        method: 'Move to archive table or delete'
      },
      {
        table: 'call_records',
        criteria: 'startedAt < NOW() - INTERVAL \'2 years\'',
        retention: '2 years',
        method: 'Archive to cold storage'
      },
      {
        table: 'payment_overview',
        criteria: 'createdAt < NOW() - INTERVAL \'7 years\'',
        retention: '7 years (legal requirement)',
        method: 'Archive with encryption'
      },
      {
        table: 'leads',
        criteria: 'status = \'ARCHIVED\' AND archivedAt < NOW() - INTERVAL \'1 year\'',
        retention: '1 year after archival',
        method: 'Soft delete or move to archive'
      }
    ]
  }
}

// Singleton instance
let optimizer: DatabaseOptimizer | null = null

export function getDatabaseOptimizer(): DatabaseOptimizer {
  if (!optimizer) {
    optimizer = new DatabaseOptimizer()
  }
  return optimizer
}
