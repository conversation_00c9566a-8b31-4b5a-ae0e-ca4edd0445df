import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const updateAnnouncementSchema = z.object({
  title: z.string().min(1, "Title is required").optional(),
  content: z.string().min(1, "Content is required").optional(),
  priority: z.enum(["LOW", "MEDIUM", "HIGH"]).optional(),
  targetAudience: z.enum(["ALL", "STUDENTS", "TEACHERS", "ACADEMIC_MANAGERS"]).optional(),
  isActive: z.boolean().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const announcement = await prisma.announcement.findUnique({
      where: { id: params.id },
      include: {
        author: {
          select: { id: true, name: true, role: true }
        }
      }
    })

    if (!announcement) {
      return NextResponse.json({ error: "Announcement not found" }, { status: 404 })
    }

    return NextResponse.json(announcement)
  } catch (error) {
    console.error("Error fetching announcement:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updateAnnouncementSchema.parse(body)

    const announcement = await prisma.announcement.findUnique({
      where: { id: params.id }
    })

    if (!announcement) {
      return NextResponse.json({ error: "Announcement not found" }, { status: 404 })
    }

    // Check if user owns this announcement or is admin/manager
    if (!["ADMIN", "MANAGER"].includes(session.user.role) && announcement.authorId !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
    }

    const updatedAnnouncement = await prisma.announcement.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        author: {
          select: { id: true, name: true, role: true }
        }
      }
    })

    // If announcement targets students, sync to students server
    if (updatedAnnouncement.targetAudience === "STUDENTS" || updatedAnnouncement.targetAudience === "ALL") {
      try {
        // TODO: Implement inter-server announcement sync
        console.log("Syncing updated announcement to students server:", updatedAnnouncement.id)
      } catch (error) {
        console.error("Error syncing announcement to students:", error)
      }
    }

    return NextResponse.json(updatedAnnouncement)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error updating announcement:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const announcement = await prisma.announcement.findUnique({
      where: { id: params.id }
    })

    if (!announcement) {
      return NextResponse.json({ error: "Announcement not found" }, { status: 404 })
    }

    // Check if user owns this announcement or is admin/manager
    if (!["ADMIN", "MANAGER"].includes(session.user.role) && announcement.authorId !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
    }

    await prisma.announcement.delete({
      where: { id: params.id }
    })

    // TODO: Remove announcement from students server if it was synced
    console.log("Removing announcement from students server:", params.id)

    return NextResponse.json({ message: "Announcement deleted successfully" })
  } catch (error) {
    console.error("Error deleting announcement:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
