import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const updateStudentSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  phone: z.string().min(1, "Phone is required").optional(),
  level: z.enum(["A1", "A2", "B1", "B2", "IELTS", "SAT", "MATH", "KIDS"]).optional(),
  branch: z.string().min(1, "Branch is required").optional(),
  emergencyContact: z.string().optional(),
  status: z.enum(["ACTIVE", "DROPPED", "PAUSED", "COMPLETED"]).optional(),
  currentGroupId: z.string().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const student = await prisma.studentReference.findUnique({
      where: { id: params.id },
      include: {
        currentGroup: {
          include: {
            course: true,
            teacher: {
              include: {
                user: true
              }
            }
          }
        },
        paymentOverview: {
          orderBy: { createdAt: "desc" },
          take: 5
        }
      }
    })

    if (!student) {
      return NextResponse.json({ error: "Student not found" }, { status: 404 })
    }

    return NextResponse.json(student)
  } catch (error) {
    console.error("Error fetching student:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "RECEPTION", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updateStudentSchema.parse(body)

    // Check if student exists
    const existingStudent = await prisma.studentReference.findUnique({
      where: { id: params.id }
    })

    if (!existingStudent) {
      return NextResponse.json({ error: "Student not found" }, { status: 404 })
    }

    // Check for phone conflicts if phone is being updated
    if (validatedData.phone && validatedData.phone !== existingStudent.phone) {
      const conflictStudent = await prisma.studentReference.findFirst({
        where: {
          AND: [
            { id: { not: params.id } },
            { phone: validatedData.phone }
          ]
        }
      })

      if (conflictStudent) {
        return NextResponse.json({ error: "Student with this phone already exists" }, { status: 400 })
      }
    }

    // Update student
    const student = await prisma.studentReference.update({
      where: { id: params.id },
      data: {
        ...validatedData,
        lastSyncedAt: new Date(),
      },
      include: {
        currentGroup: {
          include: {
            course: true,
            teacher: {
              include: {
                user: true
              }
            }
          }
        },
        paymentOverview: {
          orderBy: { createdAt: "desc" },
          take: 5
        }
      }
    })

    return NextResponse.json(student)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error updating student:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check if student exists
    const existingStudent = await prisma.studentReference.findUnique({
      where: { id: params.id }
    })

    if (!existingStudent) {
      return NextResponse.json({ error: "Student not found" }, { status: 404 })
    }

    // Delete student (cascade will handle related records)
    await prisma.studentReference.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: "Student deleted successfully" })
  } catch (error) {
    console.error("Error deleting student:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
