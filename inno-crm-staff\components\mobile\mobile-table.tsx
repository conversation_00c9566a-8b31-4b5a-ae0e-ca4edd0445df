"use client"

import { ReactNode } from 'react'
import { cn } from '@/lib/utils/cn'
import { ChevronRight } from 'lucide-react'

interface MobileTableProps {
  children: ReactNode
  className?: string
}

interface MobileTableRowProps {
  children: ReactNode
  onClick?: () => void
  className?: string
}

interface MobileTableCellProps {
  label: string
  children: ReactNode
  className?: string
  primary?: boolean
}

export function MobileTable({ children, className }: MobileTableProps) {
  return (
    <div className={cn("lg:hidden space-y-3", className)}>
      {children}
    </div>
  )
}

export function MobileTableRow({ children, onClick, className }: MobileTableRowProps) {
  const Component = onClick ? 'button' : 'div'
  
  return (
    <Component
      onClick={onClick}
      className={cn(
        "w-full bg-white rounded-lg border border-gray-200 p-4 space-y-3",
        "touch-manipulation", // Better touch targets
        onClick && "hover:bg-gray-50 transition-colors",
        className
      )}
    >
      <div className="space-y-2">
        {children}
      </div>
      {onClick && (
        <div className="flex justify-end">
          <ChevronRight className="h-4 w-4 text-gray-400" />
        </div>
      )}
    </Component>
  )
}

export function MobileTableCell({ label, children, className, primary = false }: MobileTableCellProps) {
  return (
    <div className={cn("flex justify-between items-start", className)}>
      <span className={cn(
        "text-sm font-medium text-gray-500 min-w-0 flex-shrink-0 mr-3",
        primary && "text-gray-900"
      )}>
        {label}:
      </span>
      <div className={cn(
        "text-sm text-gray-900 text-right min-w-0 flex-1",
        primary && "font-semibold"
      )}>
        {children}
      </div>
    </div>
  )
}

// Example usage component
export function StudentMobileTable({ students, onStudentClick }: {
  students: Array<{
    id: string
    name: string
    phone: string
    level: string
    status: string
    group?: string
  }>
  onStudentClick: (id: string) => void
}) {
  return (
    <MobileTable>
      {students.map((student) => (
        <MobileTableRow
          key={student.id}
          onClick={() => onStudentClick(student.id)}
        >
          <MobileTableCell label="Name" primary>
            {student.name}
          </MobileTableCell>
          <MobileTableCell label="Phone">
            {student.phone}
          </MobileTableCell>
          <MobileTableCell label="Level">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {student.level}
            </span>
          </MobileTableCell>
          <MobileTableCell label="Status">
            <span className={cn(
              "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
              student.status === 'ACTIVE' && "bg-green-100 text-green-800",
              student.status === 'PAUSED' && "bg-yellow-100 text-yellow-800",
              student.status === 'DROPPED' && "bg-red-100 text-red-800"
            )}>
              {student.status}
            </span>
          </MobileTableCell>
          {student.group && (
            <MobileTableCell label="Group">
              {student.group}
            </MobileTableCell>
          )}
        </MobileTableRow>
      ))}
    </MobileTable>
  )
}
