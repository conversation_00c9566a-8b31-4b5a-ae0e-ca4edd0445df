import crypto from 'crypto'

interface ApiClientConfig {
  baseUrl: string
  apiKey: string
  secretKey: string
}

interface GroupReference {
  id: string
  name: string
  teacherReferenceId: string
  courseName: string
  schedule: any
  room?: string
  branch: string
  startDate: string
  endDate: string
  isActive: boolean
}

interface TeacherReference {
  id: string
  name: string
  subject: string
  branch: string
  photoUrl?: string
}

interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export class StaffApiClient {
  private config: ApiClientConfig

  constructor(config: ApiClientConfig) {
    this.config = config
  }

  private generateSignature(method: string, path: string, body: string, timestamp: string): string {
    const message = `${method}|${path}|${body}|${timestamp}`
    return crypto
      .createHmac('sha256', this.config.secretKey)
      .update(message)
      .digest('hex')
  }

  private async makeRequest<T>(
    method: string,
    path: string,
    data?: any
  ): Promise<ApiResponse<T>> {
    const timestamp = new Date().toISOString()
    const body = data ? JSON.stringify(data) : ''
    const signature = this.generateSignature(method, path, body, timestamp)

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'X-API-Key': this.config.apiKey,
      'X-Server-Source': 'STUDENTS',
      'X-Signature': signature,
      'X-Timestamp': timestamp,
    }

    try {
      const response = await fetch(`${this.config.baseUrl}${path}`, {
        method,
        headers,
        body: data ? JSON.stringify(data) : undefined,
      })

      const result = await response.json()

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP ${response.status}: ${response.statusText}`,
        }
      }

      return {
        success: true,
        data: result,
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      }
    }
  }

  async getGroupReferences(): Promise<ApiResponse<GroupReference[]>> {
    return this.makeRequest<GroupReference[]>('GET', '/api/inter-server/groups')
  }

  async getGroupReference(id: string): Promise<ApiResponse<GroupReference>> {
    return this.makeRequest<GroupReference>('GET', `/api/inter-server/groups/${id}`)
  }

  async getTeacherReferences(): Promise<ApiResponse<TeacherReference[]>> {
    return this.makeRequest<TeacherReference[]>('GET', '/api/inter-server/teachers')
  }

  async getTeacherReference(id: string): Promise<ApiResponse<TeacherReference>> {
    return this.makeRequest<TeacherReference>('GET', `/api/inter-server/teachers/${id}`)
  }

  async notifyStudentUpdate(data: {
    studentId: string
    action: 'CREATED' | 'UPDATED' | 'DELETED'
    changes?: any
  }): Promise<ApiResponse<void>> {
    return this.makeRequest<void>('POST', '/api/inter-server/notifications/student-update', data)
  }

  async notifyPaymentReceived(data: {
    studentId: string
    paymentId: string
    amount: number
    method: string
    paidDate: string
  }): Promise<ApiResponse<void>> {
    return this.makeRequest<void>('POST', '/api/inter-server/notifications/payment-received', data)
  }

  async notifyAttendanceMarked(data: {
    studentId: string
    classReferenceId: string
    status: string
    date: string
  }): Promise<ApiResponse<void>> {
    return this.makeRequest<void>('POST', '/api/inter-server/notifications/attendance-marked', data)
  }

  async requestStudentData(studentId: string): Promise<ApiResponse<any>> {
    return this.makeRequest<any>('GET', `/api/inter-server/students/${studentId}/full-data`)
  }

  async syncStudentReference(studentData: {
    id: string
    name: string
    phone: string
    currentGroupId?: string
    status: string
    branch: string
    level?: string
    emergencyContact?: string
  }): Promise<ApiResponse<void>> {
    return this.makeRequest<void>('POST', '/api/inter-server/sync/students', studentData)
  }

  async healthCheck(): Promise<ApiResponse<{ status: string; timestamp: string }>> {
    return this.makeRequest<{ status: string; timestamp: string }>('GET', '/api/health')
  }
}

// Create singleton instance
let staffClient: StaffApiClient | null = null

export function getStaffClient(): StaffApiClient {
  if (!staffClient) {
    staffClient = new StaffApiClient({
      baseUrl: process.env.STAFF_SERVER_URL || 'https://staff.innovative-centre.uz',
      apiKey: process.env.STAFF_API_KEY || '',
      secretKey: process.env.STAFF_SECRET_KEY || '',
    })
  }
  return staffClient
}
