import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const createPaymentSchema = z.object({
  studentReferenceId: z.string().min(1, "Student is required"),
  amount: z.number().min(0, "Amount must be positive"),
  method: z.enum(["CASH", "CARD"]),
  status: z.enum(["PAID", "DEBT", "REFUNDED"]).default("PAID"),
  description: z.string().optional(),
  transactionId: z.string().optional(),
  dueDate: z.string().optional().transform((str) => str ? new Date(str) : undefined),
  paidDate: z.string().optional().transform((str) => str ? new Date(str) : new Date()),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const search = searchParams.get("search") || ""
    const status = searchParams.get("status") || ""
    const method = searchParams.get("method") || ""
    const studentId = searchParams.get("studentId") || ""
    const dateFrom = searchParams.get("dateFrom")
    const dateTo = searchParams.get("dateTo")

    const skip = (page - 1) * limit

    const where = {
      AND: [
        search ? {
          OR: [
            { description: { contains: search, mode: "insensitive" as const } },
            { transactionId: { contains: search, mode: "insensitive" as const } },
            { studentReference: { name: { contains: search, mode: "insensitive" as const } } },
            { studentReference: { phone: { contains: search, mode: "insensitive" as const } } },
          ]
        } : {},
        status ? { status } : {},
        method ? { method } : {},
        studentId ? { studentReferenceId: studentId } : {},
        dateFrom ? { createdAt: { gte: new Date(dateFrom) } } : {},
        dateTo ? { createdAt: { lte: new Date(dateTo) } } : {}
      ]
    }

    const [payments, total] = await Promise.all([
      prisma.paymentOverview.findMany({
        where,
        skip,
        take: limit,
        include: {
          studentReference: {
            include: {
              currentGroup: {
                include: {
                  course: true,
                  teacher: {
                    include: {
                      user: true
                    }
                  }
                }
              }
            }
          }
        },
        orderBy: { createdAt: "desc" }
      }),
      prisma.paymentOverview.count({ where })
    ])

    // Calculate summary statistics
    const summary = await prisma.paymentOverview.aggregate({
      where,
      _sum: {
        amount: true
      },
      _count: {
        _all: true
      }
    })

    const statusBreakdown = await prisma.paymentOverview.groupBy({
      by: ['status'],
      where,
      _sum: {
        amount: true
      },
      _count: {
        _all: true
      }
    })

    return NextResponse.json({
      payments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      summary: {
        totalAmount: summary._sum.amount || 0,
        totalCount: summary._count._all,
        statusBreakdown
      }
    })
  } catch (error) {
    console.error("Error fetching payments:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "CASHIER", "RECEPTION"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createPaymentSchema.parse(body)

    // Verify student exists
    const student = await prisma.studentReference.findUnique({
      where: { id: validatedData.studentReferenceId }
    })

    if (!student) {
      return NextResponse.json({ error: "Student not found" }, { status: 400 })
    }

    // Create payment
    const payment = await prisma.paymentOverview.create({
      data: validatedData,
      include: {
        studentReference: {
          include: {
            currentGroup: {
              include: {
                course: true,
                teacher: {
                  include: {
                    user: true
                  }
                }
              }
            }
          }
        }
      }
    })

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: session.user.id,
        userRole: session.user.role as any,
        action: "CREATE",
        resource: "payment",
        resourceId: payment.id,
        details: {
          amount: payment.amount,
          method: payment.method,
          studentName: student.name
        }
      }
    })

    return NextResponse.json(payment, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error creating payment:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
