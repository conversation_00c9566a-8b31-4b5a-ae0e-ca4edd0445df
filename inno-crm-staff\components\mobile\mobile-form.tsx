"use client"

import { ReactNode } from 'react'
import { cn } from '@/lib/utils/cn'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface MobileFormProps {
  children: ReactNode
  className?: string
  onSubmit?: (e: React.FormEvent) => void
}

interface MobileFormFieldProps {
  label: string
  children: ReactNode
  error?: string
  required?: boolean
  className?: string
}

interface MobileFormActionsProps {
  children: ReactNode
  className?: string
}

export function MobileForm({ children, className, onSubmit }: MobileFormProps) {
  return (
    <form 
      onSubmit={onSubmit}
      className={cn(
        "space-y-6 p-4 lg:p-6",
        className
      )}
    >
      {children}
    </form>
  )
}

export function MobileFormField({ 
  label, 
  children, 
  error, 
  required, 
  className 
}: MobileFormFieldProps) {
  return (
    <div className={cn("space-y-2", className)}>
      <Label className={cn(
        "text-sm font-medium text-gray-900",
        required && "after:content-['*'] after:text-red-500 after:ml-1"
      )}>
        {label}
      </Label>
      <div className="relative">
        {children}
      </div>
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  )
}

export function MobileFormActions({ children, className }: MobileFormActionsProps) {
  return (
    <div className={cn(
      "flex flex-col space-y-3 pt-4 border-t border-gray-200",
      "lg:flex-row lg:space-y-0 lg:space-x-3 lg:justify-end",
      className
    )}>
      {children}
    </div>
  )
}

// Enhanced input component for mobile
interface MobileInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  icon?: ReactNode
}

export function MobileInput({ 
  label, 
  error, 
  icon, 
  className, 
  ...props 
}: MobileInputProps) {
  return (
    <div className="space-y-2">
      {label && (
        <Label className="text-sm font-medium text-gray-900">
          {label}
        </Label>
      )}
      <div className="relative">
        {icon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            {icon}
          </div>
        )}
        <Input
          className={cn(
            "h-12 text-base", // Larger touch targets and text
            icon && "pl-10",
            error && "border-red-300 focus:border-red-500 focus:ring-red-500",
            className
          )}
          {...props}
        />
      </div>
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  )
}

// Enhanced button component for mobile
interface MobileButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'outline' | 'ghost' | 'destructive'
  size?: 'default' | 'sm' | 'lg'
  fullWidth?: boolean
  loading?: boolean
}

export function MobileButton({ 
  children,
  variant = 'default',
  size = 'default',
  fullWidth = false,
  loading = false,
  className,
  disabled,
  ...props 
}: MobileButtonProps) {
  return (
    <Button
      variant={variant}
      size={size}
      disabled={disabled || loading}
      className={cn(
        "h-12 text-base font-medium", // Larger touch targets
        "touch-manipulation", // Better touch response
        fullWidth && "w-full",
        className
      )}
      {...props}
    >
      {loading ? (
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          <span>Loading...</span>
        </div>
      ) : (
        children
      )}
    </Button>
  )
}
