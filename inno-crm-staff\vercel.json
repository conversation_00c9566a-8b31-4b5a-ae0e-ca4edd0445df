{"framework": "nextjs", "buildCommand": "prisma generate && npm run build", "devCommand": "npm run dev", "installCommand": "npm install", "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}], "rewrites": [{"source": "/api/inter-server/:path*", "destination": "/api/inter-server/:path*"}]}