import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const syncRequestSchema = z.object({
  apiKey: z.string().min(1, "API key is required"),
  lastSyncTime: z.string().optional().transform((str) => str ? new Date(str) : undefined),
})

const createAnnouncementSchema = z.object({
  apiKey: z.string().min(1, "API key is required"),
  title: z.string().min(1, "Title is required"),
  content: z.string().min(1, "Content is required"),
  type: z.enum(["GENERAL", "URGENT", "ACADEMIC", "PAYMENT", "EVENT"]).default("GENERAL"),
  targetAudience: z.enum(["ALL", "STUDENTS", "TEACHERS", "SPECIFIC_GROUP"]).default("ALL"),
  targetGroupId: z.string().optional(),
  expiresAt: z.string().optional().transform((str) => str ? new Date(str) : undefined),
  isActive: z.boolean().default(true),
})

// Verify API key for inter-server communication
const verifyApiKey = (apiKey: string) => {
  const validApiKey = process.env.INTER_SERVER_API_KEY
  return validApiKey && apiKey === validApiKey
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const apiKey = searchParams.get("apiKey")
    const lastSyncTime = searchParams.get("lastSyncTime")

    if (!apiKey || !verifyApiKey(apiKey)) {
      return NextResponse.json({ error: "Invalid API key" }, { status: 401 })
    }

    let whereClause: any = {
      isActive: true,
      OR: [
        { expiresAt: null },
        { expiresAt: { gt: new Date() } }
      ]
    }

    // If syncing based on last sync time
    if (lastSyncTime) {
      whereClause.AND = [
        whereClause,
        {
          OR: [
            { updatedAt: { gt: new Date(lastSyncTime) } },
            { createdAt: { gt: new Date(lastSyncTime) } }
          ]
        }
      ]
    }

    // Get announcements
    const announcements = await prisma.announcement.findMany({
      where: whereClause,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            role: true
          }
        },
        targetGroup: {
          select: {
            id: true,
            name: true,
            course: {
              select: {
                name: true,
                level: true
              }
            }
          }
        }
      },
      orderBy: { createdAt: "desc" }
    })

    // Transform data for student server
    const syncData = announcements.map(announcement => ({
      id: announcement.id,
      title: announcement.title,
      content: announcement.content,
      type: announcement.type,
      targetAudience: announcement.targetAudience,
      targetGroup: announcement.targetGroup,
      priority: announcement.priority,
      isActive: announcement.isActive,
      expiresAt: announcement.expiresAt,
      createdAt: announcement.createdAt,
      updatedAt: announcement.updatedAt,
      author: announcement.author
    }))

    return NextResponse.json({
      success: true,
      data: syncData,
      syncTime: new Date().toISOString(),
      count: syncData.length
    })
  } catch (error) {
    console.error("Error syncing announcements:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = createAnnouncementSchema.parse(body)

    // Verify API key
    if (!verifyApiKey(validatedData.apiKey)) {
      return NextResponse.json({ error: "Invalid API key" }, { status: 401 })
    }

    // Remove apiKey from data before saving
    const { apiKey, ...announcementData } = validatedData

    // Create announcement
    const announcement = await prisma.announcement.create({
      data: {
        ...announcementData,
        authorId: "system", // System-generated announcement
        priority: "MEDIUM"
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            role: true
          }
        },
        targetGroup: {
          select: {
            id: true,
            name: true,
            course: {
              select: {
                name: true,
                level: true
              }
            }
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        id: announcement.id,
        title: announcement.title,
        content: announcement.content,
        type: announcement.type,
        targetAudience: announcement.targetAudience,
        targetGroup: announcement.targetGroup,
        priority: announcement.priority,
        isActive: announcement.isActive,
        expiresAt: announcement.expiresAt,
        createdAt: announcement.createdAt,
        author: announcement.author
      }
    }, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error creating announcement:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
