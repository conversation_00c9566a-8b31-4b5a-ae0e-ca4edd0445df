"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  GraduationCap, 
  Phone, 
  DollarSign,
  Calendar,
  BarChart3,
  Pie<PERSON><PERSON>,
  Activity,
  Download
} from "lucide-react"
import DashboardLayout from "@/components/dashboard/layout"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from "recharts"

interface AnalyticsData {
  overview: {
    totalStudents: number
    activeStudents: number
    newStudentsThisPeriod: number
    studentGrowth: number
    totalLeads: number
    newLeadsThisPeriod: number
    leadGrowth: number
    conversionRate: number
    totalGroups: number
    averageGroupSize: number
    totalTeachers: number
    totalRevenue: number
    totalDebt: number
    revenueGrowth: number
  }
  charts: {
    studentsByStatus: Array<{ status: string; _count: { _all: number } }>
    studentsByLevel: Array<{ level: string; _count: { _all: number } }>
    leadsByStatus: Array<{ status: string; _count: { _all: number } }>
    teachersByTier: Array<{ tier: string; _count: { _all: number } }>
    monthlyRevenue: Array<{ month: string; revenue: number }>
    groupCapacityUtilization: Array<{
      name: string
      capacity: number
      enrolled: number
      utilization: number
    }>
  }
  recentActivities: Array<{
    id: string
    action: string
    resource: string
    userName: string
    createdAt: string
    details: any
  }>
}

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4']

const statusColors = {
  ACTIVE: '#10B981',
  DROPPED: '#EF4444',
  PAUSED: '#F59E0B',
  COMPLETED: '#3B82F6'
}

export default function AnalyticsPage() {
  const { data: session } = useSession()
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [period, setPeriod] = useState("30")
  const [branch, setBranch] = useState("")

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        period,
        ...(branch && { branch })
      })

      const response = await fetch(`/api/analytics?${params}`)
      if (response.ok) {
        const data = await response.json()
        setAnalytics(data)
      }
    } catch (error) {
      console.error("Error fetching analytics:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAnalytics()
  }, [period, branch])

  const canViewAnalytics = session?.user?.role && ["ADMIN", "MANAGER", "ACADEMIC_MANAGER"].includes(session.user.role)

  if (!canViewAnalytics) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to view analytics.</p>
        </div>
      </DashboardLayout>
    )
  }

  if (loading || !analytics) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <div className="text-lg">Loading analytics...</div>
        </div>
      </DashboardLayout>
    )
  }

  const formatGrowth = (growth: number) => {
    const isPositive = growth >= 0
    return (
      <div className={`flex items-center ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
        {isPositive ? <TrendingUp className="h-4 w-4 mr-1" /> : <TrendingDown className="h-4 w-4 mr-1" />}
        {Math.abs(growth).toFixed(1)}%
      </div>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Analytics & Reports</h1>
            <p className="text-gray-600">Comprehensive insights and performance metrics</p>
          </div>
          
          <div className="flex space-x-4">
            <Select value={period} onValueChange={setPeriod}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">Last 7 days</SelectItem>
                <SelectItem value="30">Last 30 days</SelectItem>
                <SelectItem value="90">Last 3 months</SelectItem>
                <SelectItem value="365">Last year</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={branch} onValueChange={setBranch}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="All Branches" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Branches</SelectItem>
                <SelectItem value="main">Main Branch</SelectItem>
                <SelectItem value="secondary">Secondary Branch</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Students</CardTitle>
              <GraduationCap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.overview.totalStudents}</div>
              <div className="flex items-center justify-between">
                <p className="text-xs text-muted-foreground">
                  {analytics.overview.activeStudents} active
                </p>
                {formatGrowth(analytics.overview.studentGrowth)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Leads</CardTitle>
              <Phone className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.overview.totalLeads}</div>
              <div className="flex items-center justify-between">
                <p className="text-xs text-muted-foreground">
                  {analytics.overview.conversionRate.toFixed(1)}% conversion
                </p>
                {formatGrowth(analytics.overview.leadGrowth)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${analytics.overview.totalRevenue.toFixed(0)}</div>
              <div className="flex items-center justify-between">
                <p className="text-xs text-muted-foreground">
                  ${analytics.overview.totalDebt.toFixed(0)} debt
                </p>
                {formatGrowth(analytics.overview.revenueGrowth)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Groups</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.overview.totalGroups}</div>
              <p className="text-xs text-muted-foreground">
                {analytics.overview.averageGroupSize.toFixed(1)} avg size
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts Grid */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Monthly Revenue Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Monthly Revenue
              </CardTitle>
              <CardDescription>Revenue trends over the year</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={analytics.charts.monthlyRevenue}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`$${value}`, 'Revenue']} />
                  <Area type="monotone" dataKey="revenue" stroke="#3B82F6" fill="#3B82F6" fillOpacity={0.3} />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Students by Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <PieChart className="h-5 w-5 mr-2" />
                Students by Status
              </CardTitle>
              <CardDescription>Distribution of student statuses</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <RechartsPieChart>
                  <Pie
                    data={analytics.charts.studentsByStatus.map(item => ({
                      name: item.status,
                      value: item._count._all
                    }))}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, value }) => `${name}: ${value}`}
                  >
                    {analytics.charts.studentsByStatus.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={statusColors[entry.status as keyof typeof statusColors] || COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </RechartsPieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Students by Level */}
          <Card>
            <CardHeader>
              <CardTitle>Students by Level</CardTitle>
              <CardDescription>Distribution across course levels</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={analytics.charts.studentsByLevel.map(item => ({
                  level: item.level,
                  count: item._count._all
                }))}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="level" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#10B981" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Group Capacity Utilization */}
          <Card>
            <CardHeader>
              <CardTitle>Group Capacity Utilization</CardTitle>
              <CardDescription>How full are your groups?</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={analytics.charts.groupCapacityUtilization.slice(0, 8)}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
                  <YAxis />
                  <Tooltip formatter={(value, name) => [
                    name === 'enrolled' ? `${value} students` : name === 'capacity' ? `${value} max` : `${value}%`,
                    name === 'enrolled' ? 'Enrolled' : name === 'capacity' ? 'Capacity' : 'Utilization'
                  ]} />
                  <Bar dataKey="enrolled" fill="#3B82F6" />
                  <Bar dataKey="capacity" fill="#E5E7EB" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Recent Activities
            </CardTitle>
            <CardDescription>Latest system activities and changes</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.recentActivities.slice(0, 10).map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className="flex-shrink-0">
                    <div className="h-2 w-2 bg-blue-600 rounded-full mt-2"></div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900">
                      <span className="font-medium">{activity.userName}</span> {activity.action.toLowerCase()}d a {activity.resource}
                    </p>
                    <p className="text-xs text-gray-500">
                      {new Date(activity.createdAt).toLocaleString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
