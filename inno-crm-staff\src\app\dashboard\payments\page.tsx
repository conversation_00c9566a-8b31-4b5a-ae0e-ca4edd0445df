"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Eye, 
  DollarSign, 
  CreditCard, 
  TrendingUp, 
  TrendingDown,
  Calendar,
  User,
  Download
} from "lucide-react"
import DashboardLayout from "@/components/dashboard/layout"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"

const createPaymentSchema = z.object({
  studentReferenceId: z.string().min(1, "Student is required"),
  amount: z.number().min(0, "Amount must be positive"),
  method: z.enum(["CASH", "CARD"]),
  status: z.enum(["PAID", "DEBT", "REFUNDED"]).default("PAID"),
  description: z.string().optional(),
  transactionId: z.string().optional(),
  dueDate: z.string().optional(),
  paidDate: z.string().optional(),
})

type CreatePaymentForm = z.infer<typeof createPaymentSchema>

interface Payment {
  id: string
  amount: number
  method: string
  status: string
  description?: string
  transactionId?: string
  dueDate?: string
  paidDate?: string
  createdAt: string
  studentReference: {
    id: string
    name: string
    phone: string
    currentGroup?: {
      name: string
      course: {
        name: string
      }
      teacher: {
        user: {
          name: string
        }
      }
    }
  }
}

interface Student {
  id: string
  name: string
  phone: string
}

interface PaymentSummary {
  totalAmount: number
  totalCount: number
  statusBreakdown: Array<{
    status: string
    _sum: { amount: number }
    _count: { _all: number }
  }>
}

const statusLabels = {
  PAID: "Paid",
  DEBT: "Debt",
  REFUNDED: "Refunded"
}

const statusColors = {
  PAID: "bg-green-100 text-green-800",
  DEBT: "bg-red-100 text-red-800",
  REFUNDED: "bg-yellow-100 text-yellow-800"
}

const methodLabels = {
  CASH: "Cash",
  CARD: "Card"
}

export default function PaymentsManagementPage() {
  const { data: session } = useSession()
  const [payments, setPayments] = useState<Payment[]>([])
  const [students, setStudents] = useState<Student[]>([])
  const [summary, setSummary] = useState<PaymentSummary | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("")
  const [methodFilter, setMethodFilter] = useState("")
  const [dateFromFilter, setDateFromFilter] = useState("")
  const [dateToFilter, setDateToFilter] = useState("")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  })

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
  } = useForm<CreatePaymentForm>({
    resolver: zodResolver(createPaymentSchema),
  })

  const fetchPayments = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && { status: statusFilter }),
        ...(methodFilter && { method: methodFilter }),
        ...(dateFromFilter && { dateFrom: dateFromFilter }),
        ...(dateToFilter && { dateTo: dateToFilter })
      })

      const response = await fetch(`/api/payments?${params}`)
      if (response.ok) {
        const data = await response.json()
        setPayments(data.payments)
        setPagination(data.pagination)
        setSummary(data.summary)
      }
    } catch (error) {
      console.error("Error fetching payments:", error)
    } finally {
      setLoading(false)
    }
  }

  const fetchStudents = async () => {
    try {
      const response = await fetch("/api/students?limit=1000")
      if (response.ok) {
        const data = await response.json()
        setStudents(data.students)
      }
    } catch (error) {
      console.error("Error fetching students:", error)
    }
  }

  useEffect(() => {
    fetchPayments()
  }, [pagination.page, searchTerm, statusFilter, methodFilter, dateFromFilter, dateToFilter])

  useEffect(() => {
    fetchStudents()
  }, [])

  const onSubmit = async (data: CreatePaymentForm) => {
    try {
      const response = await fetch("/api/payments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          amount: parseFloat(data.amount.toString())
        }),
      })

      if (response.ok) {
        setIsCreateDialogOpen(false)
        reset()
        fetchPayments()
      } else {
        const error = await response.json()
        console.error("Error creating payment:", error)
      }
    } catch (error) {
      console.error("Error creating payment:", error)
    }
  }

  const handleDelete = async (paymentId: string) => {
    if (!confirm("Are you sure you want to delete this payment?")) return

    try {
      const response = await fetch(`/api/payments/${paymentId}`, {
        method: "DELETE",
      })

      if (response.ok) {
        fetchPayments()
      } else {
        const error = await response.json()
        console.error("Error deleting payment:", error)
      }
    } catch (error) {
      console.error("Error deleting payment:", error)
    }
  }

  const canManagePayments = session?.user?.role && ["ADMIN", "MANAGER", "CASHIER", "RECEPTION"].includes(session.user.role)

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Payment Management</h1>
            <p className="text-gray-600">Track and manage student payments and financial records</p>
          </div>
          
          {canManagePayments && (
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Record Payment
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Record New Payment</DialogTitle>
                  <DialogDescription>
                    Add a new payment record for a student.
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                  <div>
                    <Label htmlFor="studentReferenceId">Student</Label>
                    <Select onValueChange={(value) => setValue("studentReferenceId", value)}>
                      <SelectTrigger className={errors.studentReferenceId ? "border-red-500" : ""}>
                        <SelectValue placeholder="Select student" />
                      </SelectTrigger>
                      <SelectContent>
                        {students.map((student) => (
                          <SelectItem key={student.id} value={student.id}>
                            {student.name} ({student.phone})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.studentReferenceId && (
                      <p className="text-sm text-red-500 mt-1">{errors.studentReferenceId.message}</p>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="amount">Amount</Label>
                      <Input
                        id="amount"
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        {...register("amount", { valueAsNumber: true })}
                        className={errors.amount ? "border-red-500" : ""}
                      />
                      {errors.amount && (
                        <p className="text-sm text-red-500 mt-1">{errors.amount.message}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="method">Payment Method</Label>
                      <Select onValueChange={(value) => setValue("method", value as any)}>
                        <SelectTrigger className={errors.method ? "border-red-500" : ""}>
                          <SelectValue placeholder="Select method" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="CASH">Cash</SelectItem>
                          <SelectItem value="CARD">Card</SelectItem>
                        </SelectContent>
                      </Select>
                      {errors.method && (
                        <p className="text-sm text-red-500 mt-1">{errors.method.message}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="status">Status</Label>
                    <Select onValueChange={(value) => setValue("status", value as any)} defaultValue="PAID">
                      <SelectTrigger className={errors.status ? "border-red-500" : ""}>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="PAID">Paid</SelectItem>
                        <SelectItem value="DEBT">Debt</SelectItem>
                        <SelectItem value="REFUNDED">Refunded</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.status && (
                      <p className="text-sm text-red-500 mt-1">{errors.status.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="description">Description (Optional)</Label>
                    <Input
                      id="description"
                      placeholder="e.g., Monthly tuition, Registration fee"
                      {...register("description")}
                    />
                  </div>

                  <div>
                    <Label htmlFor="transactionId">Transaction ID (Optional)</Label>
                    <Input
                      id="transactionId"
                      placeholder="e.g., TXN123456"
                      {...register("transactionId")}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="dueDate">Due Date (Optional)</Label>
                      <Input
                        id="dueDate"
                        type="date"
                        {...register("dueDate")}
                      />
                    </div>

                    <div>
                      <Label htmlFor="paidDate">Paid Date</Label>
                      <Input
                        id="paidDate"
                        type="date"
                        defaultValue={new Date().toISOString().split('T')[0]}
                        {...register("paidDate")}
                      />
                    </div>
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsCreateDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? "Recording..." : "Record Payment"}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          )}
        </div>

        {/* Summary Cards */}
        {summary && (
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">${summary.totalAmount.toFixed(2)}</div>
                <p className="text-xs text-muted-foreground">
                  {summary.totalCount} transactions
                </p>
              </CardContent>
            </Card>

            {summary.statusBreakdown.map((status) => (
              <Card key={status.status}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {statusLabels[status.status as keyof typeof statusLabels]}
                  </CardTitle>
                  {status.status === 'PAID' ? (
                    <TrendingUp className="h-4 w-4 text-green-600" />
                  ) : status.status === 'DEBT' ? (
                    <TrendingDown className="h-4 w-4 text-red-600" />
                  ) : (
                    <CreditCard className="h-4 w-4 text-yellow-600" />
                  )}
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">${status._sum.amount?.toFixed(2) || '0.00'}</div>
                  <p className="text-xs text-muted-foreground">
                    {status._count._all} payments
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Payments Table */}
        <Card>
          <CardHeader>
            <CardTitle>Payment Records</CardTitle>
            <CardDescription>
              View and manage all payment transactions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4 mb-6">
              <div className="flex-1 min-w-64">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search payments..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Status</SelectItem>
                  {Object.entries(statusLabels).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={methodFilter} onValueChange={setMethodFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Methods</SelectItem>
                  {Object.entries(methodLabels).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Input
                type="date"
                placeholder="From date"
                value={dateFromFilter}
                onChange={(e) => setDateFromFilter(e.target.value)}
                className="w-40"
              />
              <Input
                type="date"
                placeholder="To date"
                value={dateToFilter}
                onChange={(e) => setDateToFilter(e.target.value)}
                className="w-40"
              />
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>

            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Student</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Method</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        Loading...
                      </TableCell>
                    </TableRow>
                  ) : payments.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        No payments found
                      </TableCell>
                    </TableRow>
                  ) : (
                    payments.map((payment) => (
                      <TableRow key={payment.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{payment.studentReference.name}</div>
                            <div className="text-sm text-gray-500">
                              {payment.studentReference.currentGroup ? (
                                `${payment.studentReference.currentGroup.name} • ${payment.studentReference.currentGroup.course.name}`
                              ) : (
                                "No group assigned"
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">${payment.amount.toFixed(2)}</div>
                        </TableCell>
                        <TableCell>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            {methodLabels[payment.method as keyof typeof methodLabels]}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[payment.status as keyof typeof statusColors]}`}>
                            {statusLabels[payment.status as keyof typeof statusLabels]}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="max-w-32 truncate">
                            {payment.description || "-"}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {payment.paidDate ? new Date(payment.paidDate).toLocaleDateString() : new Date(payment.createdAt).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            {canManagePayments && (
                              <>
                                <Button variant="ghost" size="sm">
                                  <Edit className="h-4 w-4" />
                                </Button>
                                {session?.user?.role && ["ADMIN", "MANAGER"].includes(session.user.role) && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleDelete(payment.id)}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                )}
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            {pagination.pages > 1 && (
              <div className="flex items-center justify-between mt-4">
                <p className="text-sm text-gray-700">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to{" "}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
                  {pagination.total} results
                </p>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                    disabled={pagination.page === 1}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                    disabled={pagination.page === pagination.pages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
