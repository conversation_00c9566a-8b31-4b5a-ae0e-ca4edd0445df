generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Student-specific models
model User {
  id        String   @id @default(cuid())
  email     String?  @unique
  phone     String   @unique
  name      String
  role      Role     @default(STUDENT)
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  studentProfile Student?
  messages       Message[] @relation("MessageRecipients")

  @@map("users")
}

model Student {
  id                      String        @id @default(cuid())
  userId                  String        @unique
  level                   Level         @default(A1)
  branch                  String
  emergencyContact        String?
  photoUrl                String?
  dateOfBirth             DateTime?
  address                 String?
  status                  StudentStatus @default(ACTIVE)
  currentGroupReferenceId String?
  droppedAt               DateTime?
  pausedAt                DateTime?
  resumedAt               DateTime?
  reEnrollmentNotes       String?
  lastContactedAt         DateTime?
  createdAt               DateTime      @default(now())
  updatedAt               DateTime      @updatedAt

  // Relations
  user                  User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  currentGroupReference GroupReference? @relation("StudentCurrentGroup", fields: [currentGroupReferenceId], references: [id])
  enrollments           Enrollment[]
  payments              Payment[]
  attendances           Attendance[]
  assessments           Assessment[]

  @@map("students")
}

// Reference tables for staff data
model GroupReference {
  id                 String   @id // Same ID as in staff database
  name               String
  teacherReferenceId String
  courseName         String
  schedule           Json
  room               String?
  branch             String
  startDate          DateTime
  endDate            DateTime
  isActive           Boolean  @default(true)
  lastSyncedAt       DateTime @default(now())
  syncVersion        Int      @default(1)

  // Relations
  teacherReference TeacherReference @relation(fields: [teacherReferenceId], references: [id])
  currentStudents  Student[]        @relation("StudentCurrentGroup")
  enrollments      Enrollment[]
  attendances      Attendance[]
  assessments      Assessment[]

  @@map("group_references")
}

model TeacherReference {
  id           String   @id // Same ID as in staff database
  name         String
  subject      String
  branch       String
  photoUrl     String?
  lastSyncedAt DateTime @default(now())
  syncVersion  Int      @default(1)

  // Relations
  groupReferences GroupReference[]

  @@map("teacher_references")
}

model Enrollment {
  id               String           @id @default(cuid())
  studentId        String
  groupReferenceId String
  status           EnrollmentStatus @default(ACTIVE)
  startDate        DateTime
  endDate          DateTime?
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt

  // Relations
  student        Student        @relation(fields: [studentId], references: [id])
  groupReference GroupReference @relation(fields: [groupReferenceId], references: [id])

  @@unique([studentId, groupReferenceId])
  @@map("enrollments")
}

model Payment {
  id            String        @id @default(cuid())
  studentId     String
  amount        Decimal
  method        PaymentMethod
  status        PaymentStatus @default(PAID)
  description   String?
  transactionId String?
  dueDate       DateTime?
  paidDate      DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  student Student @relation(fields: [studentId], references: [id])

  @@map("payments")
}

model Attendance {
  id               String           @id @default(cuid())
  studentId        String
  classReferenceId String // Reference to class in staff DB
  groupReferenceId String
  status           AttendanceStatus @default(PRESENT)
  notes            String?
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt

  // Relations
  student        Student        @relation(fields: [studentId], references: [id])
  groupReference GroupReference @relation(fields: [groupReferenceId], references: [id])

  @@unique([studentId, classReferenceId])
  @@map("attendances")
}

model Assessment {
  id               String         @id @default(cuid())
  studentId        String?
  groupReferenceId String?
  testName         String
  type             AssessmentType
  level            Level?
  score            Int?
  maxScore         Int?
  passed           Boolean        @default(false)
  questions        Json?
  results          Json?
  assignedBy       String? // Teacher/Admin who assigned
  assignedAt       DateTime?
  startedAt        DateTime?
  completedAt      DateTime?
  branch           String         @default("main")
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt

  // Relations
  student        Student?        @relation(fields: [studentId], references: [id])
  groupReference GroupReference? @relation(fields: [groupReferenceId], references: [id])

  @@map("assessments")
}

model Message {
  id                String    @id @default(cuid())
  subject           String
  content           String
  recipientType     String // 'ALL', 'STUDENTS', 'SPECIFIC'
  recipientIds      String[] // Array of user IDs for specific recipients
  priority          String    @default("MEDIUM") // 'LOW', 'MEDIUM', 'HIGH'
  status            String    @default("DRAFT") // 'DRAFT', 'SENT', 'FAILED'
  sentAt            DateTime?
  senderReferenceId String // Reference to sender in staff DB
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relations (for recipients only)
  recipients User[] @relation("MessageRecipients")

  @@map("messages")
}

// Enums
enum Role {
  STUDENT
}

enum Level {
  A1
  A2
  B1
  B2
  IELTS
  SAT
  MATH
  KIDS
}

enum StudentStatus {
  ACTIVE
  DROPPED
  PAUSED
  COMPLETED
}

enum EnrollmentStatus {
  ACTIVE
  COMPLETED
  DROPPED
  SUSPENDED
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  EXCUSED
}

enum PaymentMethod {
  CASH
  CARD
}

enum PaymentStatus {
  PAID
  DEBT
  REFUNDED
}

enum AssessmentType {
  LEVEL_TEST
  PROGRESS_TEST
  FINAL_EXAM
  GROUP_TEST
}
