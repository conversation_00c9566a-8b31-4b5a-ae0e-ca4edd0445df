import { NextRequest, NextResponse } from 'next/server'
import { createInterServerAuthMiddleware, createRateLimitMiddleware, combineMiddleware } from '@/lib/middleware/inter-server-auth'
import { getStudentsClient } from '@/lib/api-clients/students-client'

// Configure middleware
const authMiddleware = createInterServerAuthMiddleware({
  validApiKeys: [process.env.STUDENTS_API_KEY || ''],
  secretKey: process.env.STUDENTS_SECRET_KEY || '',
})

const rateLimitMiddleware = createRateLimitMiddleware({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 100, // 100 requests per minute
})

const middleware = combineMiddleware(authMiddleware, rateLimitMiddleware)

export async function POST(request: NextRequest) {
  // Apply middleware
  const middlewareResponse = await middleware(request)
  if (middlewareResponse) {
    return middlewareResponse
  }

  try {
    const data = await request.json()
    
    // Validate required fields
    const { name, phone, level, branch } = data
    if (!name || !phone || !level || !branch) {
      return NextResponse.json(
        { error: 'Missing required fields: name, phone, level, branch' },
        { status: 400 }
      )
    }

    // Create student in students server
    const studentsClient = getStudentsClient()
    const result = await studentsClient.createStudent(data)

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      )
    }

    // TODO: Create student reference in staff database
    // This would be implemented when we have the Prisma schema set up
    
    return NextResponse.json(result.data, { status: 201 })
  } catch (error) {
    console.error('Error creating student:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  // Apply middleware
  const middlewareResponse = await middleware(request)
  if (middlewareResponse) {
    return middlewareResponse
  }

  try {
    // TODO: Get students from staff database with pagination
    // This would return student references from the staff database
    
    const students = [
      // Mock data for now
      {
        id: '1',
        name: 'John Doe',
        phone: '+998901234567',
        status: 'ACTIVE',
        branch: 'main',
        level: 'B1',
        currentGroupId: 'group-1'
      }
    ]

    return NextResponse.json(students)
  } catch (error) {
    console.error('Error fetching students:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
