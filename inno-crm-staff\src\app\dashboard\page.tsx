"use client"

import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Users, 
  GraduationCap, 
  Phone, 
  DollarSign, 
  TrendingUp, 
  Calendar,
  Plus,
  Eye
} from "lucide-react"
import Link from "next/link"
import DashboardLayout from "@/components/dashboard/layout"

// Mock data - in real app, this would come from API
const stats = [
  {
    title: "Total Students",
    value: "1,234",
    change: "+12%",
    changeType: "positive" as const,
    icon: GraduationCap,
  },
  {
    title: "Active Leads",
    value: "89",
    change: "+5%",
    changeType: "positive" as const,
    icon: Phone,
  },
  {
    title: "Monthly Revenue",
    value: "$45,231",
    change: "+18%",
    changeType: "positive" as const,
    icon: DollarSign,
  },
  {
    title: "Staff Members",
    value: "24",
    change: "+2",
    changeType: "positive" as const,
    icon: Users,
  },
]

const recentActivities = [
  {
    id: 1,
    type: "lead",
    message: "New lead assigned to <PERSON>",
    time: "2 minutes ago",
  },
  {
    id: 2,
    type: "payment",
    message: "Payment received from <PERSON>",
    time: "15 minutes ago",
  },
  {
    id: 3,
    type: "enrollment",
    message: "Student enrolled in IELTS group",
    time: "1 hour ago",
  },
  {
    id: 4,
    type: "group",
    message: "New group created for B2 level",
    time: "2 hours ago",
  },
]

const quickActions = [
  {
    title: "Add New Student",
    description: "Register a new student",
    href: "/dashboard/students/new",
    icon: Plus,
  },
  {
    title: "Create Group",
    description: "Set up a new class group",
    href: "/dashboard/groups/new",
    icon: Calendar,
  },
  {
    title: "View Leads",
    description: "Manage potential students",
    href: "/dashboard/leads",
    icon: Eye,
  },
  {
    title: "Analytics",
    description: "View detailed reports",
    href: "/dashboard/analytics",
    icon: TrendingUp,
  },
]

export default function DashboardPage() {
  const { data: session } = useSession()

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">
            Welcome back, {session?.user?.name}. Here&apos;s what&apos;s happening today.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat) => (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <stat.icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  <span className={stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}>
                    {stat.change}
                  </span>
                  {" "}from last month
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Common tasks you can perform
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                {quickActions.map((action) => (
                  <Link key={action.title} href={action.href}>
                    <div className="flex flex-col items-center p-4 text-center border rounded-lg hover:bg-gray-50 transition-colors">
                      <action.icon className="h-8 w-8 mb-2 text-blue-600" />
                      <h3 className="font-medium text-sm">{action.title}</h3>
                      <p className="text-xs text-gray-500 mt-1">{action.description}</p>
                    </div>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Activities */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activities</CardTitle>
              <CardDescription>
                Latest updates from your team
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivities.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <div className="h-2 w-2 bg-blue-600 rounded-full mt-2"></div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900">{activity.message}</p>
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/dashboard/activities">View all activities</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
