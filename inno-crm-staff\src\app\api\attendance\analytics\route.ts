import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "TEACHER", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const groupId = searchParams.get("groupId")
    const studentId = searchParams.get("studentId")
    const teacherId = searchParams.get("teacherId")
    const period = parseInt(searchParams.get("period") || "30") // days
    const branch = searchParams.get("branch")

    const startDate = new Date()
    startDate.setDate(startDate.getDate() - period)

    let whereClause: any = {
      date: { gte: startDate }
    }

    // Apply filters
    if (groupId) whereClause.groupId = groupId
    if (teacherId) whereClause.group = { teacherId }
    if (branch) whereClause.group = { ...whereClause.group, branch }

    // Get attendance records
    const attendanceRecords = await prisma.attendanceRecord.findMany({
      where: whereClause,
      include: {
        group: {
          include: {
            course: true,
            teacher: {
              include: {
                user: true
              }
            }
          }
        },
        studentAttendances: {
          include: {
            studentReference: true
          },
          ...(studentId && {
            where: {
              studentReferenceId: studentId
            }
          })
        }
      }
    })

    // Calculate overall statistics
    const totalSessions = attendanceRecords.length
    const totalStudentAttendances = attendanceRecords.reduce(
      (sum, record) => sum + record.studentAttendances.length, 0
    )

    const attendanceByStatus = attendanceRecords.reduce((acc, record) => {
      record.studentAttendances.forEach(attendance => {
        acc[attendance.status] = (acc[attendance.status] || 0) + 1
      })
      return acc
    }, {} as Record<string, number>)

    const attendanceRate = totalStudentAttendances > 0 ? 
      ((attendanceByStatus.PRESENT || 0) / totalStudentAttendances * 100) : 0

    // Group analytics by group
    const groupAnalytics = attendanceRecords.reduce((acc, record) => {
      const groupId = record.groupId
      if (!acc[groupId]) {
        acc[groupId] = {
          groupId,
          groupName: record.group.name,
          courseName: record.group.course.name,
          teacherName: record.group.teacher.user.name,
          totalSessions: 0,
          totalAttendances: 0,
          statusCounts: { PRESENT: 0, ABSENT: 0, LATE: 0, EXCUSED: 0 }
        }
      }

      acc[groupId].totalSessions++
      acc[groupId].totalAttendances += record.studentAttendances.length

      record.studentAttendances.forEach(attendance => {
        acc[groupId].statusCounts[attendance.status]++
      })

      return acc
    }, {} as Record<string, any>)

    // Calculate attendance rates for each group
    Object.values(groupAnalytics).forEach((group: any) => {
      group.attendanceRate = group.totalAttendances > 0 ? 
        (group.statusCounts.PRESENT / group.totalAttendances * 100) : 0
    })

    // Student analytics (if specific student requested)
    let studentAnalytics = null
    if (studentId) {
      const studentAttendances = attendanceRecords.flatMap(record => 
        record.studentAttendances.filter(att => att.studentReferenceId === studentId)
      )

      const studentStatusCounts = studentAttendances.reduce((acc, attendance) => {
        acc[attendance.status] = (acc[attendance.status] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      studentAnalytics = {
        studentId,
        totalSessions: studentAttendances.length,
        statusCounts: studentStatusCounts,
        attendanceRate: studentAttendances.length > 0 ? 
          ((studentStatusCounts.PRESENT || 0) / studentAttendances.length * 100) : 0
      }
    }

    // Daily attendance trends (last 30 days)
    const dailyTrends = []
    for (let i = period - 1; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      const dateStr = date.toISOString().split('T')[0]

      const dayRecords = attendanceRecords.filter(record => 
        record.date.toISOString().split('T')[0] === dateStr
      )

      const dayAttendances = dayRecords.flatMap(record => record.studentAttendances)
      const presentCount = dayAttendances.filter(att => att.status === 'PRESENT').length
      const totalCount = dayAttendances.length

      dailyTrends.push({
        date: dateStr,
        totalSessions: dayRecords.length,
        totalAttendances: totalCount,
        presentCount,
        attendanceRate: totalCount > 0 ? (presentCount / totalCount * 100) : 0
      })
    }

    // Top performing groups (by attendance rate)
    const topGroups = Object.values(groupAnalytics)
      .sort((a: any, b: any) => b.attendanceRate - a.attendanceRate)
      .slice(0, 5)

    // Students with low attendance (if not filtering by specific student)
    let lowAttendanceStudents = []
    if (!studentId) {
      const studentAttendanceMap = new Map()

      attendanceRecords.forEach(record => {
        record.studentAttendances.forEach(attendance => {
          const studentId = attendance.studentReferenceId
          if (!studentAttendanceMap.has(studentId)) {
            studentAttendanceMap.set(studentId, {
              studentId,
              studentName: attendance.studentReference.name,
              totalSessions: 0,
              presentSessions: 0
            })
          }

          const student = studentAttendanceMap.get(studentId)
          student.totalSessions++
          if (attendance.status === 'PRESENT') {
            student.presentSessions++
          }
        })
      })

      lowAttendanceStudents = Array.from(studentAttendanceMap.values())
        .map((student: any) => ({
          ...student,
          attendanceRate: student.totalSessions > 0 ? 
            (student.presentSessions / student.totalSessions * 100) : 0
        }))
        .filter((student: any) => student.attendanceRate < 80 && student.totalSessions >= 3)
        .sort((a: any, b: any) => a.attendanceRate - b.attendanceRate)
        .slice(0, 10)
    }

    return NextResponse.json({
      overview: {
        totalSessions,
        totalStudentAttendances,
        attendanceRate: Math.round(attendanceRate * 100) / 100,
        statusBreakdown: attendanceByStatus
      },
      groupAnalytics: Object.values(groupAnalytics),
      studentAnalytics,
      dailyTrends,
      topGroups,
      lowAttendanceStudents,
      period,
      generatedAt: new Date().toISOString()
    })
  } catch (error) {
    console.error("Error fetching attendance analytics:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
