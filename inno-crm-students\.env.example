# Database Configuration
DATABASE_URL="postgresql://username:password@host:port/database?sslmode=require"

# NextAuth Configuration
NEXTAUTH_SECRET="your-students-secret-key-here-minimum-32-characters"
NEXTAUTH_URL="https://students.innovative-centre.uz"

# Inter-server Communication
STAFF_SERVER_URL="https://staff.innovative-centre.uz"
STAFF_API_KEY="staff-server-api-key"
STAFF_SECRET_KEY="staff-server-secret-key"

# Application Configuration
APP_NAME="Innovative Centre - Student Portal"
APP_ENV="production"
NODE_ENV="production"

# Security Configuration
ENCRYPTION_KEY="your-encryption-key-here"
API_RATE_LIMIT="200"

# Monitoring & Logging
LOG_LEVEL="info"
ENABLE_METRICS="true"

# Email Configuration (Optional)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# File Upload Configuration
MAX_FILE_SIZE="5242880"
ALLOWED_FILE_TYPES="image/jpeg,image/png,application/pdf"

# Feature Flags
ENABLE_NOTIFICATIONS="true"
ENABLE_OFFLINE_MODE="true"
ENABLE_PWA="true"

# Student Portal Specific
ENABLE_ASSESSMENTS="true"
ENABLE_PROGRESS_TRACKING="true"
ENABLE_MESSAGING="true"
