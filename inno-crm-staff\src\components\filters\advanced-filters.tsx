"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { 
  Filter, 
  X, 
  CalendarIcon, 
  Download,
  RefreshCw
} from "lucide-react"
import { format } from "date-fns"

interface FilterField {
  key: string
  label: string
  type: "text" | "select" | "date" | "dateRange" | "number"
  options?: { value: string; label: string }[]
  placeholder?: string
}

interface FilterValue {
  key: string
  value: any
  label: string
  operator?: "equals" | "contains" | "gte" | "lte" | "between"
}

interface AdvancedFiltersProps {
  fields: FilterField[]
  onFiltersChange: (filters: Record<string, any>) => void
  onExport?: (filters: Record<string, any>) => void
  exportFormats?: string[]
  className?: string
}

export default function AdvancedFilters({ 
  fields, 
  onFiltersChange, 
  onExport,
  exportFormats = ["csv", "xlsx", "json"],
  className = "" 
}: AdvancedFiltersProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [activeFilters, setActiveFilters] = useState<FilterValue[]>([])
  const [tempFilters, setTempFilters] = useState<Record<string, any>>({})
  const [exportFormat, setExportFormat] = useState("csv")

  const addFilter = (field: FilterField, value: any, operator = "equals") => {
    if (!value || (Array.isArray(value) && value.length === 0)) return

    const filterValue: FilterValue = {
      key: field.key,
      value,
      label: `${field.label}: ${formatFilterValue(value, field.type)}`,
      operator
    }

    const newFilters = [...activeFilters.filter(f => f.key !== field.key), filterValue]
    setActiveFilters(newFilters)
    
    const filtersObject = newFilters.reduce((acc, filter) => {
      acc[filter.key] = filter.value
      return acc
    }, {} as Record<string, any>)
    
    onFiltersChange(filtersObject)
  }

  const removeFilter = (key: string) => {
    const newFilters = activeFilters.filter(f => f.key !== key)
    setActiveFilters(newFilters)
    
    const filtersObject = newFilters.reduce((acc, filter) => {
      acc[filter.key] = filter.value
      return acc
    }, {} as Record<string, any>)
    
    onFiltersChange(filtersObject)
  }

  const clearAllFilters = () => {
    setActiveFilters([])
    setTempFilters({})
    onFiltersChange({})
  }

  const handleExport = () => {
    if (onExport) {
      const filtersObject = activeFilters.reduce((acc, filter) => {
        acc[filter.key] = filter.value
        return acc
      }, {} as Record<string, any>)
      
      onExport({ ...filtersObject, format: exportFormat })
    }
  }

  const formatFilterValue = (value: any, type: string): string => {
    if (type === "date" && value instanceof Date) {
      return format(value, "MMM dd, yyyy")
    }
    if (type === "dateRange" && Array.isArray(value) && value.length === 2) {
      return `${format(value[0], "MMM dd")} - ${format(value[1], "MMM dd, yyyy")}`
    }
    if (Array.isArray(value)) {
      return value.join(", ")
    }
    return String(value)
  }

  const renderFilterInput = (field: FilterField) => {
    const value = tempFilters[field.key]

    switch (field.type) {
      case "text":
        return (
          <div className="space-y-2">
            <Input
              placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}`}
              value={value || ""}
              onChange={(e) => setTempFilters(prev => ({ ...prev, [field.key]: e.target.value }))}
              onKeyDown={(e) => {
                if (e.key === "Enter" && value) {
                  addFilter(field, value, "contains")
                  setTempFilters(prev => ({ ...prev, [field.key]: "" }))
                }
              }}
            />
            <Button 
              size="sm" 
              onClick={() => {
                if (value) {
                  addFilter(field, value, "contains")
                  setTempFilters(prev => ({ ...prev, [field.key]: "" }))
                }
              }}
              disabled={!value}
            >
              Add Filter
            </Button>
          </div>
        )

      case "select":
        return (
          <Select onValueChange={(selectedValue) => {
            addFilter(field, selectedValue)
            setTempFilters(prev => ({ ...prev, [field.key]: "" }))
          }}>
            <SelectTrigger>
              <SelectValue placeholder={`Select ${field.label.toLowerCase()}`} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )

      case "date":
        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-full justify-start text-left font-normal">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {value ? format(value, "PPP") : `Select ${field.label.toLowerCase()}`}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={value}
                onSelect={(date) => {
                  if (date) {
                    addFilter(field, date)
                    setTempFilters(prev => ({ ...prev, [field.key]: null }))
                  }
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        )

      case "number":
        return (
          <div className="space-y-2">
            <Input
              type="number"
              placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}`}
              value={value || ""}
              onChange={(e) => setTempFilters(prev => ({ ...prev, [field.key]: e.target.value }))}
              onKeyDown={(e) => {
                if (e.key === "Enter" && value) {
                  addFilter(field, Number(value))
                  setTempFilters(prev => ({ ...prev, [field.key]: "" }))
                }
              }}
            />
            <Button 
              size="sm" 
              onClick={() => {
                if (value) {
                  addFilter(field, Number(value))
                  setTempFilters(prev => ({ ...prev, [field.key]: "" }))
                }
              }}
              disabled={!value}
            >
              Add Filter
            </Button>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className={className}>
      {/* Active Filters Display */}
      {activeFilters.length > 0 && (
        <div className="flex flex-wrap items-center gap-2 mb-4">
          <span className="text-sm font-medium text-gray-700">Active filters:</span>
          {activeFilters.map((filter) => (
            <Badge key={filter.key} variant="secondary" className="flex items-center gap-1">
              {filter.label}
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 ml-1"
                onClick={() => removeFilter(filter.key)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-red-600 hover:text-red-700"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Clear all
          </Button>
        </div>
      )}

      {/* Filter Controls */}
      <div className="flex items-center gap-2">
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Advanced Filters
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Advanced Filters</DialogTitle>
              <DialogDescription>
                Add multiple filters to refine your search results
              </DialogDescription>
            </DialogHeader>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
              {fields.map((field) => (
                <div key={field.key} className="space-y-2">
                  <Label htmlFor={field.key}>{field.label}</Label>
                  {renderFilterInput(field)}
                </div>
              ))}
            </div>
          </DialogContent>
        </Dialog>

        {onExport && (
          <div className="flex items-center gap-2">
            <Select value={exportFormat} onValueChange={setExportFormat}>
              <SelectTrigger className="w-24">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {exportFormats.map((format) => (
                  <SelectItem key={format} value={format}>
                    {format.toUpperCase()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm" onClick={handleExport}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
