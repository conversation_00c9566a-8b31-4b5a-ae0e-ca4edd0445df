import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const createStudentSchema = z.object({
  name: z.string().min(1, "Name is required"),
  phone: z.string().min(1, "Phone is required"),
  level: z.enum(["A1", "A2", "B1", "B2", "IELTS", "SAT", "MATH", "KIDS"]),
  branch: z.string().min(1, "Branch is required"),
  emergencyContact: z.string().optional(),
  status: z.enum(["ACTIVE", "DROPPED", "PAUSED", "COMPLETED"]).default("ACTIVE"),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const search = searchParams.get("search") || ""
    const status = searchParams.get("status") || ""
    const level = searchParams.get("level") || ""
    const branch = searchParams.get("branch") || ""

    const skip = (page - 1) * limit

    const where = {
      AND: [
        search ? {
          OR: [
            { name: { contains: search, mode: "insensitive" as const } },
            { phone: { contains: search, mode: "insensitive" as const } },
          ]
        } : {},
        status ? { status } : {},
        level ? { level } : {},
        branch ? { branch } : {}
      ]
    }

    const [students, total] = await Promise.all([
      prisma.studentReference.findMany({
        where,
        skip,
        take: limit,
        include: {
          currentGroup: {
            include: {
              course: true,
              teacher: {
                include: {
                  user: true
                }
              }
            }
          },
          paymentOverview: {
            orderBy: { createdAt: "desc" },
            take: 1
          }
        },
        orderBy: { createdAt: "desc" }
      }),
      prisma.studentReference.count({ where })
    ])

    return NextResponse.json({
      students,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error("Error fetching students:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "RECEPTION"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createStudentSchema.parse(body)

    // Check if student already exists
    const existingStudent = await prisma.studentReference.findFirst({
      where: { phone: validatedData.phone }
    })

    if (existingStudent) {
      return NextResponse.json({ error: "Student with this phone already exists" }, { status: 400 })
    }

    // Create student reference
    const student = await prisma.studentReference.create({
      data: {
        ...validatedData,
        lastSyncedAt: new Date(),
      },
      include: {
        currentGroup: {
          include: {
            course: true,
            teacher: {
              include: {
                user: true
              }
            }
          }
        }
      }
    })

    return NextResponse.json(student, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error creating student:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
