# API Documentation

This document describes the REST API endpoints available in the Inno CRM system.

## Authentication

All API endpoints require authentication via NextAuth.js session cookies. Include the session cookie in your requests.

### Authentication Headers
```
Cookie: next-auth.session-token=<session-token>
```

## Base URLs

- **Staff API**: `http://localhost:3000/api`
- **Student API**: `http://localhost:3001/api`

## Common Response Format

### Success Response
```json
{
  "data": {},
  "message": "Success message",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Error Response
```json
{
  "error": "Error message",
  "details": {},
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Paginated Response
```json
{
  "data": [],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "pages": 10
  }
}
```

## Staff API Endpoints

### Students

#### GET /api/students
Get list of students with filtering and pagination.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `search` (string): Search by name or phone
- `status` (string): Filter by status (ACTIVE, INACTIVE, DROPPED, GRADUATED)
- `level` (string): Filter by level (A1, A2, B1, B2, IELTS, SAT, MATH, KIDS)
- `branch` (string): Filter by branch

**Response:**
```json
{
  "students": [
    {
      "id": "student-id",
      "name": "John Doe",
      "phone": "1234567890",
      "status": "ACTIVE",
      "level": "A1",
      "branch": "main",
      "currentGroup": {
        "id": "group-id",
        "name": "Group A1-1",
        "course": {
          "name": "English A1"
        }
      },
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 50,
    "pages": 5
  }
}
```

#### POST /api/students
Create a new student.

**Request Body:**
```json
{
  "name": "Jane Doe",
  "phone": "0987654321",
  "status": "ACTIVE",
  "level": "A2",
  "branch": "main",
  "groupId": "group-id"
}
```

#### GET /api/students/[id]
Get student details by ID.

#### PUT /api/students/[id]
Update student information.

#### DELETE /api/students/[id]
Delete a student (soft delete).

### Groups

#### GET /api/groups
Get list of groups.

**Query Parameters:**
- `page`, `limit`: Pagination
- `search`: Search by name
- `courseId`: Filter by course
- `teacherId`: Filter by teacher
- `isActive`: Filter by active status

#### POST /api/groups
Create a new group.

**Request Body:**
```json
{
  "name": "Group A1-2",
  "courseId": "course-id",
  "teacherId": "teacher-id",
  "room": "Room 101",
  "schedule": "Mon, Wed, Fri 10:00-12:00",
  "maxStudents": 15
}
```

### Courses

#### GET /api/courses
Get list of courses.

#### POST /api/courses
Create a new course.

**Request Body:**
```json
{
  "name": "English A1",
  "description": "Beginner English course",
  "level": "A1",
  "duration": 120,
  "price": 500000
}
```

### Payments

#### GET /api/payments
Get payment history.

#### POST /api/payments
Record a new payment.

**Request Body:**
```json
{
  "studentReferenceId": "student-id",
  "amount": 100000,
  "method": "CASH",
  "description": "Monthly payment"
}
```

### Attendance

#### GET /api/attendance
Get attendance records.

#### POST /api/attendance
Record attendance.

**Request Body:**
```json
{
  "studentReferenceId": "student-id",
  "groupId": "group-id",
  "date": "2024-01-01",
  "status": "PRESENT"
}
```

### Assessments

#### GET /api/assessments
Get list of assessments.

#### POST /api/assessments
Create a new assessment.

**Request Body:**
```json
{
  "studentReferenceId": "student-id",
  "testName": "Level Test A1",
  "type": "LEVEL_TEST",
  "level": "A1",
  "maxScore": 100,
  "questions": [
    {
      "id": "q1",
      "question": "What is your name?",
      "type": "text",
      "points": 5
    }
  ]
}
```

### Messages

#### GET /api/messages
Get messages.

#### POST /api/messages
Send a new message.

**Request Body:**
```json
{
  "subject": "Important Notice",
  "content": "Please attend tomorrow's class.",
  "recipientType": "STUDENTS",
  "priority": "HIGH",
  "sendNow": true
}
```

### Global Search

#### GET /api/search/global
Perform global search across all entities.

**Query Parameters:**
- `query` (string): Search term
- `types` (string): Comma-separated entity types
- `limit` (number): Max results

**Response:**
```json
{
  "results": [
    {
      "id": "entity-id",
      "type": "student",
      "title": "John Doe",
      "subtitle": "1234567890",
      "description": "Group A1-1 - English A1",
      "url": "/dashboard/students/entity-id",
      "relevance": 95
    }
  ],
  "total": 1,
  "query": "john"
}
```

### Data Export

#### POST /api/export
Export data in various formats.

**Request Body:**
```json
{
  "type": "students",
  "format": "csv",
  "filters": {
    "status": "ACTIVE",
    "level": "A1"
  },
  "dateRange": {
    "from": "2024-01-01",
    "to": "2024-12-31"
  }
}
```

## Student API Endpoints

### Profile

#### GET /api/profile
Get student profile information.

#### PUT /api/profile
Update student profile.

### Courses

#### GET /api/courses
Get enrolled courses.

### Assessments

#### GET /api/assessments
Get assigned assessments.

#### PUT /api/assessments/[id]
Submit assessment answers.

**Request Body:**
```json
{
  "action": "submit_answers",
  "answers": [
    {
      "questionId": "q1",
      "answer": "My name is John"
    }
  ],
  "completedAt": "2024-01-01T12:00:00.000Z"
}
```

### Messages

#### GET /api/messages
Get received messages.

#### GET /api/messages/[id]
Get specific message details.

### Attendance

#### GET /api/attendance
Get attendance history.

### Payments

#### GET /api/payments
Get payment history.

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input data |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 409 | Conflict - Resource already exists |
| 422 | Unprocessable Entity - Validation failed |
| 500 | Internal Server Error - Server error |

## Rate Limiting

API endpoints are rate-limited to prevent abuse:
- **General endpoints**: 100 requests per minute
- **Search endpoints**: 50 requests per minute
- **Export endpoints**: 10 requests per minute

## Webhooks

The system supports webhooks for real-time notifications:

### Student Enrollment
```json
{
  "event": "student.enrolled",
  "data": {
    "studentId": "student-id",
    "groupId": "group-id",
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

### Payment Received
```json
{
  "event": "payment.received",
  "data": {
    "paymentId": "payment-id",
    "studentId": "student-id",
    "amount": 100000,
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

## SDK Examples

### JavaScript/Node.js
```javascript
const response = await fetch('/api/students', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
  },
  credentials: 'include', // Include session cookies
});

const data = await response.json();
```

### cURL
```bash
curl -X GET \
  'http://localhost:3000/api/students?page=1&limit=10' \
  -H 'Content-Type: application/json' \
  -b 'next-auth.session-token=<session-token>'
```
