import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const createGroupSchema = z.object({
  name: z.string().min(1, "Name is required"),
  courseId: z.string().min(1, "Course is required"),
  teacherId: z.string().min(1, "Teacher is required"),
  capacity: z.number().min(1, "Capacity must be at least 1").default(20),
  schedule: z.string(), // JSON string for schedule
  room: z.string().optional(),
  cabinetId: z.string().optional(),
  branch: z.string().min(1, "Branch is required"),
  startDate: z.string().transform((str) => new Date(str)),
  endDate: z.string().transform((str) => new Date(str)),
  isActive: z.boolean().default(true),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const search = searchParams.get("search") || ""
    const courseId = searchParams.get("courseId") || ""
    const teacherId = searchParams.get("teacherId") || ""
    const isActive = searchParams.get("isActive")

    const skip = (page - 1) * limit

    const where = {
      AND: [
        search ? {
          OR: [
            { name: { contains: search, mode: "insensitive" as const } },
            { room: { contains: search, mode: "insensitive" as const } },
          ]
        } : {},
        courseId ? { courseId } : {},
        teacherId ? { teacherId } : {},
        isActive !== null ? { isActive: isActive === "true" } : {}
      ]
    }

    const [groups, total] = await Promise.all([
      prisma.group.findMany({
        where,
        skip,
        take: limit,
        include: {
          course: true,
          teacher: {
            include: {
              user: true
            }
          },
          cabinet: true,
          studentReferences: true,
          _count: {
            select: {
              studentReferences: true
            }
          }
        },
        orderBy: { createdAt: "desc" }
      }),
      prisma.group.count({ where })
    ])

    return NextResponse.json({
      groups,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error("Error fetching groups:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createGroupSchema.parse(body)

    // Check if group name already exists
    const existingGroup = await prisma.group.findFirst({
      where: { name: validatedData.name }
    })

    if (existingGroup) {
      return NextResponse.json({ error: "Group with this name already exists" }, { status: 400 })
    }

    // Verify course exists
    const course = await prisma.course.findUnique({
      where: { id: validatedData.courseId }
    })

    if (!course) {
      return NextResponse.json({ error: "Course not found" }, { status: 400 })
    }

    // Verify teacher exists
    const teacher = await prisma.teacher.findUnique({
      where: { id: validatedData.teacherId }
    })

    if (!teacher) {
      return NextResponse.json({ error: "Teacher not found" }, { status: 400 })
    }

    // Create group
    const group = await prisma.group.create({
      data: {
        ...validatedData,
        schedule: JSON.parse(validatedData.schedule), // Parse JSON string
      },
      include: {
        course: true,
        teacher: {
          include: {
            user: true
          }
        },
        cabinet: true,
        _count: {
          select: {
            studentReferences: true
          }
        }
      }
    })

    return NextResponse.json(group, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error creating group:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
