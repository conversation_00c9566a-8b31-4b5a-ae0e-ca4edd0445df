"use client"

import { useState, useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { 
  Search, 
  Users, 
  GraduationCap, 
  Phone, 
  Calendar, 
  BookOpen, 
  MessageSquare, 
  Megaphone,
  Loader2,
  ArrowRight,
  Filter
} from "lucide-react"
import { useDebounce } from "@/hooks/use-debounce"

interface SearchResult {
  id: string
  type: string
  title: string
  subtitle?: string
  description?: string
  url: string
  metadata?: Record<string, any>
  relevance: number
}

interface GlobalSearchProps {
  placeholder?: string
  className?: string
  showDialog?: boolean
}

const typeIcons = {
  student: GraduationCap,
  staff: Users,
  lead: Phone,
  group: Calendar,
  course: Book<PERSON><PERSON>,
  message: Message<PERSON>quare,
  announcement: Megaphone
}

const typeLabels = {
  student: "Student",
  staff: "Staff",
  lead: "Lead",
  group: "Group",
  course: "Course",
  message: "Message",
  announcement: "Announcement"
}

const typeColors = {
  student: "bg-blue-100 text-blue-800",
  staff: "bg-green-100 text-green-800",
  lead: "bg-orange-100 text-orange-800",
  group: "bg-purple-100 text-purple-800",
  course: "bg-indigo-100 text-indigo-800",
  message: "bg-yellow-100 text-yellow-800",
  announcement: "bg-red-100 text-red-800"
}

export default function GlobalSearch({ placeholder = "Search everything...", className = "", showDialog = false }: GlobalSearchProps) {
  const [query, setQuery] = useState("")
  const [results, setResults] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])
  const debouncedQuery = useDebounce(query, 300)
  const router = useRouter()
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (debouncedQuery.length >= 2) {
      performSearch(debouncedQuery)
    } else {
      setResults([])
    }
  }, [debouncedQuery, selectedTypes])

  const performSearch = async (searchQuery: string) => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        query: searchQuery,
        limit: "20"
      })

      if (selectedTypes.length > 0) {
        params.append("types", selectedTypes.join(","))
      }

      const response = await fetch(`/api/search/global?${params}`)
      if (response.ok) {
        const data = await response.json()
        setResults(data.results || [])
      }
    } catch (error) {
      console.error("Error performing search:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleResultClick = (result: SearchResult) => {
    router.push(result.url)
    setIsOpen(false)
    setQuery("")
  }

  const toggleType = (type: string) => {
    setSelectedTypes(prev => 
      prev.includes(type) 
        ? prev.filter(t => t !== type)
        : [...prev, type]
    )
  }

  const SearchInput = () => (
    <div className="relative">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
      <Input
        ref={inputRef}
        placeholder={placeholder}
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        className={`pl-10 ${className}`}
        onFocus={() => setIsOpen(true)}
      />
      {loading && (
        <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 animate-spin" />
      )}
    </div>
  )

  const SearchResults = () => (
    <div className="space-y-2">
      {/* Type Filters */}
      <div className="flex flex-wrap gap-2 p-2 border-b">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setSelectedTypes([])}
          className={selectedTypes.length === 0 ? "bg-blue-100" : ""}
        >
          All
        </Button>
        {Object.entries(typeLabels).map(([type, label]) => {
          const Icon = typeIcons[type as keyof typeof typeIcons]
          return (
            <Button
              key={type}
              variant="ghost"
              size="sm"
              onClick={() => toggleType(type)}
              className={selectedTypes.includes(type) ? "bg-blue-100" : ""}
            >
              <Icon className="h-3 w-3 mr-1" />
              {label}
            </Button>
          )
        })}
      </div>

      {/* Results */}
      <div className="max-h-96 overflow-y-auto">
        {results.length === 0 && query.length >= 2 && !loading && (
          <div className="text-center py-8 text-gray-500">
            No results found for "{query}"
          </div>
        )}
        
        {results.map((result) => {
          const Icon = typeIcons[result.type as keyof typeof typeIcons] || Search
          return (
            <div
              key={`${result.type}-${result.id}`}
              className="p-3 hover:bg-gray-50 cursor-pointer border-b last:border-b-0 transition-colors"
              onClick={() => handleResultClick(result)}
            >
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <Icon className="h-5 w-5 text-gray-400 mt-0.5" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <h3 className="text-sm font-medium text-gray-900 truncate">
                      {result.title}
                    </h3>
                    <Badge className={typeColors[result.type as keyof typeof typeColors] || "bg-gray-100 text-gray-800"}>
                      {typeLabels[result.type as keyof typeof typeLabels] || result.type}
                    </Badge>
                  </div>
                  {result.subtitle && (
                    <p className="text-xs text-gray-600 mb-1">{result.subtitle}</p>
                  )}
                  {result.description && (
                    <p className="text-xs text-gray-500 line-clamp-2">{result.description}</p>
                  )}
                </div>
                <div className="flex-shrink-0">
                  <ArrowRight className="h-4 w-4 text-gray-400" />
                </div>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )

  if (showDialog) {
    return (
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" className={className}>
            <Search className="h-4 w-4 mr-2" />
            {placeholder}
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Search</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <SearchInput />
            <SearchResults />
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <div className="relative">
      <SearchInput />
      
      {isOpen && query.length >= 2 && (
        <Card className="absolute top-full left-0 right-0 mt-1 z-50 shadow-lg">
          <CardContent className="p-0">
            <SearchResults />
          </CardContent>
        </Card>
      )}
      
      {/* Backdrop to close search */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  )
}
