import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const globalSearchSchema = z.object({
  query: z.string().min(1, "Search query is required"),
  types: z.array(z.enum(["students", "staff", "leads", "groups", "courses", "messages", "announcements"])).optional(),
  limit: z.number().min(1).max(50).default(10),
  includeInactive: z.boolean().default(false),
})

interface SearchResult {
  id: string
  type: string
  title: string
  subtitle?: string
  description?: string
  url: string
  metadata?: Record<string, any>
  relevance: number
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const query = searchParams.get("query") || ""
    const types = searchParams.get("types")?.split(",") || ["students", "staff", "leads", "groups", "courses", "messages", "announcements"]
    const limit = parseInt(searchParams.get("limit") || "10")
    const includeInactive = searchParams.get("includeInactive") === "true"

    if (!query) {
      return NextResponse.json({ results: [], total: 0 })
    }

    const results: SearchResult[] = []

    // Search Students
    if (types.includes("students")) {
      const students = await prisma.studentReference.findMany({
        where: {
          AND: [
            {
              OR: [
                { name: { contains: query, mode: "insensitive" } },
                { phone: { contains: query, mode: "insensitive" } },
              ]
            },
            includeInactive ? {} : { status: { not: "DROPPED" } }
          ]
        },
        include: {
          currentGroup: {
            include: {
              course: true
            }
          }
        },
        take: limit
      })

      students.forEach(student => {
        results.push({
          id: student.id,
          type: "student",
          title: student.name,
          subtitle: student.phone,
          description: student.currentGroup ? `${student.currentGroup.name} - ${student.currentGroup.course.name}` : "No group assigned",
          url: `/dashboard/students/${student.id}`,
          metadata: {
            status: student.status,
            level: student.level,
            branch: student.branch
          },
          relevance: calculateRelevance(query, [student.name, student.phone])
        })
      })
    }

    // Search Staff
    if (types.includes("staff")) {
      const staff = await prisma.user.findMany({
        where: {
          OR: [
            { name: { contains: query, mode: "insensitive" } },
            { phone: { contains: query, mode: "insensitive" } },
            { email: { contains: query, mode: "insensitive" } },
          ]
        },
        include: {
          teacherProfile: true
        },
        take: limit
      })

      staff.forEach(user => {
        results.push({
          id: user.id,
          type: "staff",
          title: user.name,
          subtitle: user.email || user.phone,
          description: `${user.role}${user.teacherProfile ? " - Teacher" : ""}`,
          url: `/dashboard/staff/${user.id}`,
          metadata: {
            role: user.role,
            isTeacher: !!user.teacherProfile
          },
          relevance: calculateRelevance(query, [user.name, user.phone, user.email || ""])
        })
      })
    }

    // Search Leads
    if (types.includes("leads")) {
      const leads = await prisma.lead.findMany({
        where: {
          OR: [
            { name: { contains: query, mode: "insensitive" } },
            { phone: { contains: query, mode: "insensitive" } },
            { coursePreference: { contains: query, mode: "insensitive" } },
          ]
        },
        take: limit
      })

      leads.forEach(lead => {
        results.push({
          id: lead.id,
          type: "lead",
          title: lead.name,
          subtitle: lead.phone,
          description: `${lead.status} - Interested in ${lead.coursePreference}`,
          url: `/dashboard/leads/${lead.id}`,
          metadata: {
            status: lead.status,
            coursePreference: lead.coursePreference,
            source: lead.source
          },
          relevance: calculateRelevance(query, [lead.name, lead.phone, lead.coursePreference])
        })
      })
    }

    // Search Groups
    if (types.includes("groups")) {
      const groups = await prisma.group.findMany({
        where: {
          AND: [
            {
              OR: [
                { name: { contains: query, mode: "insensitive" } },
                { room: { contains: query, mode: "insensitive" } },
              ]
            },
            includeInactive ? {} : { isActive: true }
          ]
        },
        include: {
          course: true,
          teacher: {
            include: {
              user: true
            }
          },
          _count: {
            select: {
              studentReferences: true
            }
          }
        },
        take: limit
      })

      groups.forEach(group => {
        results.push({
          id: group.id,
          type: "group",
          title: group.name,
          subtitle: group.course.name,
          description: `${group.teacher.user.name} - ${group._count.studentReferences} students`,
          url: `/dashboard/groups/${group.id}`,
          metadata: {
            course: group.course.name,
            teacher: group.teacher.user.name,
            studentCount: group._count.studentReferences,
            isActive: group.isActive
          },
          relevance: calculateRelevance(query, [group.name, group.room || "", group.course.name])
        })
      })
    }

    // Search Courses
    if (types.includes("courses")) {
      const courses = await prisma.course.findMany({
        where: {
          AND: [
            {
              OR: [
                { name: { contains: query, mode: "insensitive" } },
                { description: { contains: query, mode: "insensitive" } },
              ]
            },
            includeInactive ? {} : { isActive: true }
          ]
        },
        include: {
          _count: {
            select: {
              groups: true
            }
          }
        },
        take: limit
      })

      courses.forEach(course => {
        results.push({
          id: course.id,
          type: "course",
          title: course.name,
          subtitle: course.level,
          description: `${course.description || ""} - ${course._count.groups} groups`,
          url: `/dashboard/courses/${course.id}`,
          metadata: {
            level: course.level,
            duration: course.duration,
            price: course.price,
            groupCount: course._count.groups,
            isActive: course.isActive
          },
          relevance: calculateRelevance(query, [course.name, course.description || "", course.level])
        })
      })
    }

    // Search Messages
    if (types.includes("messages")) {
      const messages = await prisma.message.findMany({
        where: {
          OR: [
            { subject: { contains: query, mode: "insensitive" } },
            { content: { contains: query, mode: "insensitive" } },
          ]
        },
        include: {
          sender: {
            select: { name: true }
          }
        },
        take: limit,
        orderBy: { createdAt: "desc" }
      })

      messages.forEach(message => {
        results.push({
          id: message.id,
          type: "message",
          title: message.subject,
          subtitle: `From: ${message.sender.name}`,
          description: message.content.substring(0, 100) + (message.content.length > 100 ? "..." : ""),
          url: `/dashboard/messages/${message.id}`,
          metadata: {
            status: message.status,
            priority: message.priority,
            recipientType: message.recipientType,
            sentAt: message.sentAt
          },
          relevance: calculateRelevance(query, [message.subject, message.content])
        })
      })
    }

    // Search Announcements
    if (types.includes("announcements")) {
      const announcements = await prisma.announcement.findMany({
        where: {
          AND: [
            {
              OR: [
                { title: { contains: query, mode: "insensitive" } },
                { content: { contains: query, mode: "insensitive" } },
              ]
            },
            includeInactive ? {} : { isActive: true }
          ]
        },
        include: {
          author: {
            select: { name: true }
          }
        },
        take: limit,
        orderBy: { createdAt: "desc" }
      })

      announcements.forEach(announcement => {
        results.push({
          id: announcement.id,
          type: "announcement",
          title: announcement.title,
          subtitle: `By: ${announcement.author.name}`,
          description: announcement.content.substring(0, 100) + (announcement.content.length > 100 ? "..." : ""),
          url: `/dashboard/announcements/${announcement.id}`,
          metadata: {
            priority: announcement.priority,
            targetAudience: announcement.targetAudience,
            isActive: announcement.isActive
          },
          relevance: calculateRelevance(query, [announcement.title, announcement.content])
        })
      })
    }

    // Sort by relevance
    results.sort((a, b) => b.relevance - a.relevance)

    return NextResponse.json({
      results: results.slice(0, limit),
      total: results.length,
      query,
      types
    })
  } catch (error) {
    console.error("Error performing global search:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

function calculateRelevance(query: string, fields: string[]): number {
  const queryLower = query.toLowerCase()
  let relevance = 0

  fields.forEach(field => {
    const fieldLower = field.toLowerCase()
    
    // Exact match gets highest score
    if (fieldLower === queryLower) {
      relevance += 100
    }
    // Starts with query gets high score
    else if (fieldLower.startsWith(queryLower)) {
      relevance += 50
    }
    // Contains query gets medium score
    else if (fieldLower.includes(queryLower)) {
      relevance += 25
    }
    // Word boundary match gets bonus
    if (new RegExp(`\\b${queryLower}`, 'i').test(fieldLower)) {
      relevance += 10
    }
  })

  return relevance
}
