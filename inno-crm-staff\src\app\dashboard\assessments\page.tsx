"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Textarea } from "@/components/ui/textarea"
import { 
  Plus, 
  Search, 
  Filter, 
  FileText, 
  Clock, 
  CheckCircle, 
  XCircle,
  Eye,
  Edit,
  Trash2,
  GraduationCap,
  BarChart3
} from "lucide-react"
import DashboardLayout from "@/components/dashboard/layout"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"

const createAssessmentSchema = z.object({
  studentReferenceId: z.string().min(1, "Student is required"),
  groupId: z.string().optional(),
  testName: z.string().min(1, "Test name is required"),
  type: z.enum(["LEVEL_TEST", "PROGRESS_TEST", "FINAL_EXAM", "GROUP_TEST"]),
  level: z.enum(["A1", "A2", "B1", "B2", "IELTS", "SAT", "MATH", "KIDS"]).optional(),
  maxScore: z.number().min(1, "Max score must be at least 1").default(100),
})

type CreateAssessmentForm = z.infer<typeof createAssessmentSchema>

interface Assessment {
  id: string
  studentReferenceId: string
  groupId?: string
  testName: string
  type: string
  level?: string
  score?: number
  maxScore: number
  passed: boolean
  assignedBy: string
  assignedAt: string
  startedAt?: string
  completedAt?: string
  createdAt: string
  student?: {
    name: string
  }
  group?: {
    name: string
    course: {
      name: string
    }
  }
}

interface Student {
  id: string
  name: string
  level?: string
}

interface Group {
  id: string
  name: string
  course: {
    name: string
  }
  teacher: {
    user: {
      name: string
    }
  }
}

const assessmentTypes = {
  LEVEL_TEST: "Level Test",
  PROGRESS_TEST: "Progress Test", 
  FINAL_EXAM: "Final Exam",
  GROUP_TEST: "Group Test"
}

const levels = ["A1", "A2", "B1", "B2", "IELTS", "SAT", "MATH", "KIDS"]

const getStatusBadge = (assessment: Assessment) => {
  if (assessment.score !== null && assessment.score !== undefined) {
    return (
      <Badge variant={assessment.passed ? "default" : "destructive"}>
        {assessment.passed ? "Passed" : "Failed"} ({assessment.score}/{assessment.maxScore})
      </Badge>
    )
  } else if (assessment.completedAt) {
    return <Badge variant="secondary">Completed - Pending Grade</Badge>
  } else if (assessment.startedAt) {
    return <Badge variant="outline">In Progress</Badge>
  } else {
    return <Badge variant="secondary">Assigned</Badge>
  }
}

export default function AssessmentsManagementPage() {
  const { data: session } = useSession()
  const [assessments, setAssessments] = useState<Assessment[]>([])
  const [students, setStudents] = useState<Student[]>([])
  const [groups, setGroups] = useState<Group[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState("")
  const [levelFilter, setLevelFilter] = useState("")
  const [statusFilter, setStatusFilter] = useState("")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    watch,
    setValue
  } = useForm<CreateAssessmentForm>({
    resolver: zodResolver(createAssessmentSchema)
  })

  useEffect(() => {
    fetchAssessments()
    fetchStudents()
    fetchGroups()
  }, [])

  const fetchAssessments = async () => {
    try {
      const response = await fetch("/api/assessments")
      if (response.ok) {
        const data = await response.json()
        setAssessments(data.assessments || [])
      }
    } catch (error) {
      console.error("Error fetching assessments:", error)
    } finally {
      setLoading(false)
    }
  }

  const fetchStudents = async () => {
    try {
      const response = await fetch("/api/students")
      if (response.ok) {
        const data = await response.json()
        setStudents(data.students || [])
      }
    } catch (error) {
      console.error("Error fetching students:", error)
    }
  }

  const fetchGroups = async () => {
    try {
      const response = await fetch("/api/groups")
      if (response.ok) {
        const data = await response.json()
        setGroups(data.groups || [])
      }
    } catch (error) {
      console.error("Error fetching groups:", error)
    }
  }

  const onSubmit = async (data: CreateAssessmentForm) => {
    try {
      const response = await fetch("/api/assessments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        await fetchAssessments()
        setIsCreateDialogOpen(false)
        reset()
      } else {
        const error = await response.json()
        console.error("Error creating assessment:", error)
      }
    } catch (error) {
      console.error("Error creating assessment:", error)
    }
  }

  const filteredAssessments = assessments.filter(assessment => {
    const matchesSearch = assessment.testName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         assessment.student?.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = !typeFilter || assessment.type === typeFilter
    const matchesLevel = !levelFilter || assessment.level === levelFilter
    
    let matchesStatus = true
    if (statusFilter === "assigned") {
      matchesStatus = !assessment.startedAt
    } else if (statusFilter === "in_progress") {
      matchesStatus = !!assessment.startedAt && !assessment.completedAt
    } else if (statusFilter === "completed") {
      matchesStatus = !!assessment.completedAt && (assessment.score === null || assessment.score === undefined)
    } else if (statusFilter === "graded") {
      matchesStatus = assessment.score !== null && assessment.score !== undefined
    }

    return matchesSearch && matchesType && matchesLevel && matchesStatus
  })

  const canCreateAssessment = session?.user?.role && ["ADMIN", "MANAGER", "TEACHER", "ACADEMIC_MANAGER"].includes(session.user.role)

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading assessments...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Assessment Management</h1>
            <p className="text-gray-600">Create and manage student assessments and tests</p>
          </div>
          
          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              <BarChart3 className="h-4 w-4 mr-2" />
              Analytics
            </Button>
            
            {canCreateAssessment && (
              <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                <DialogTrigger asChild>
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Create Assessment
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>Create New Assessment</DialogTitle>
                    <DialogDescription>
                      Assign a new assessment to a student
                    </DialogDescription>
                  </DialogHeader>
                  
                  <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                    <div>
                      <Label htmlFor="studentReferenceId">Student</Label>
                      <Select onValueChange={(value) => setValue("studentReferenceId", value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select student" />
                        </SelectTrigger>
                        <SelectContent>
                          {students.map((student) => (
                            <SelectItem key={student.id} value={student.id}>
                              {student.name} {student.level && `(${student.level})`}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.studentReferenceId && (
                        <p className="text-sm text-red-600 mt-1">{errors.studentReferenceId.message}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="groupId">Group (Optional)</Label>
                      <Select onValueChange={(value) => setValue("groupId", value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select group" />
                        </SelectTrigger>
                        <SelectContent>
                          {groups.map((group) => (
                            <SelectItem key={group.id} value={group.id}>
                              {group.name} - {group.course.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="testName">Test Name</Label>
                      <Input
                        id="testName"
                        {...register("testName")}
                        placeholder="e.g., Level Assessment A1"
                      />
                      {errors.testName && (
                        <p className="text-sm text-red-600 mt-1">{errors.testName.message}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="type">Assessment Type</Label>
                      <Select onValueChange={(value) => setValue("type", value as any)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.entries(assessmentTypes).map(([value, label]) => (
                            <SelectItem key={value} value={value}>
                              {label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.type && (
                        <p className="text-sm text-red-600 mt-1">{errors.type.message}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="level">Level (Optional)</Label>
                      <Select onValueChange={(value) => setValue("level", value as any)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select level" />
                        </SelectTrigger>
                        <SelectContent>
                          {levels.map((level) => (
                            <SelectItem key={level} value={level}>
                              {level}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="maxScore">Max Score</Label>
                      <Input
                        id="maxScore"
                        type="number"
                        {...register("maxScore", { valueAsNumber: true })}
                        placeholder="100"
                      />
                      {errors.maxScore && (
                        <p className="text-sm text-red-600 mt-1">{errors.maxScore.message}</p>
                      )}
                    </div>

                    <div className="flex justify-end space-x-2 pt-4">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsCreateDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button type="submit" disabled={isSubmitting}>
                        {isSubmitting ? "Creating..." : "Create Assessment"}
                      </Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>
            )}
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div>
                <Label htmlFor="search">Search</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="search"
                    placeholder="Search assessments..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="type-filter">Type</Label>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All types</SelectItem>
                    {Object.entries(assessmentTypes).map(([value, label]) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="level-filter">Level</Label>
                <Select value={levelFilter} onValueChange={setLevelFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All levels" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All levels</SelectItem>
                    {levels.map((level) => (
                      <SelectItem key={level} value={level}>
                        {level}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="status-filter">Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All statuses</SelectItem>
                    <SelectItem value="assigned">Assigned</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="graded">Graded</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-end">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm("")
                    setTypeFilter("")
                    setLevelFilter("")
                    setStatusFilter("")
                  }}
                  className="w-full"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Clear Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Assessments Table */}
        <Card>
          <CardHeader>
            <CardTitle>Assessments ({filteredAssessments.length})</CardTitle>
            <CardDescription>
              Manage and track student assessments and test results
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredAssessments.length === 0 ? (
              <div className="text-center py-8">
                <GraduationCap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No assessments found</h3>
                <p className="text-gray-600 mb-4">
                  {assessments.length === 0
                    ? "Get started by creating your first assessment."
                    : "Try adjusting your search criteria."
                  }
                </p>
                {canCreateAssessment && assessments.length === 0 && (
                  <Button onClick={() => setIsCreateDialogOpen(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create First Assessment
                  </Button>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Test Name</TableHead>
                      <TableHead>Student</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Level</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Assigned</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAssessments.map((assessment) => (
                      <TableRow key={assessment.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{assessment.testName}</div>
                            {assessment.group && (
                              <div className="text-sm text-gray-500">
                                {assessment.group.name} - {assessment.group.course.name}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">{assessment.student?.name || "Unknown"}</div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {assessmentTypes[assessment.type as keyof typeof assessmentTypes]}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {assessment.level && (
                            <Badge variant="secondary">{assessment.level}</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(assessment)}
                        </TableCell>
                        <TableCell>
                          <div className="text-sm text-gray-600">
                            {new Date(assessment.assignedAt).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            {canCreateAssessment && (
                              <>
                                <Button variant="ghost" size="sm">
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="sm">
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
