@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  /* Improve touch targets */
  button, a, input, select, textarea {
    min-height: 44px;
  }

  /* Better text readability on mobile */
  body {
    font-size: 16px;
    line-height: 1.5;
  }

  /* Prevent zoom on input focus */
  input, select, textarea {
    font-size: 16px;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Better table scrolling */
  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* PWA specific styles */
@media (display-mode: standalone) {
  /* Hide elements that don't make sense in standalone mode */
  .browser-only {
    display: none;
  }

  /* Add safe area padding for devices with notches */
  body {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}

/* Touch-friendly interactions */
.touch-manipulation {
  touch-action: manipulation;
}

/* Improved focus states for accessibility */
button:focus-visible,
a:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}
