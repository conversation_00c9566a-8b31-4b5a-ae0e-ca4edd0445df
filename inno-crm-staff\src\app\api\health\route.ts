import { NextRequest, NextResponse } from 'next/server'
import { getStudentsClient } from '@/lib/api-clients/students-client'

export async function GET(request: NextRequest) {
  const checks: Record<string, { status: string; message?: string; responseTime?: number }> = {}
  
  try {
    // Database check
    const dbStart = Date.now()
    try {
      // TODO: Add actual database connection check using Prisma
      // await prisma.$queryRaw`SELECT 1`
      checks.database = {
        status: 'ok',
        responseTime: Date.now() - dbStart
      }
    } catch (error) {
      checks.database = {
        status: 'error',
        message: error instanceof Error ? error.message : 'Database connection failed',
        responseTime: Date.now() - dbStart
      }
    }

    // Inter-server communication check
    const interServerStart = Date.now()
    try {
      const studentsClient = getStudentsClient()
      const result = await studentsClient.healthCheck()
      
      checks.interServerComm = {
        status: result.success ? 'ok' : 'error',
        message: result.success ? undefined : result.error,
        responseTime: Date.now() - interServerStart
      }
    } catch (error) {
      checks.interServerComm = {
        status: 'error',
        message: error instanceof Error ? error.message : 'Inter-server communication failed',
        responseTime: Date.now() - interServerStart
      }
    }

    // VPN access check (if enabled)
    if (process.env.VPN_REQUIRED === 'true') {
      const clientIp = request.headers.get('x-forwarded-for') || 
                      request.headers.get('x-real-ip') || 
                      'unknown'
      
      const allowedRanges = process.env.ALLOWED_IP_RANGES?.split(',') || []
      const isVpnAccess = allowedRanges.some(range => {
        // Simple IP range check (in production, use proper CIDR validation)
        return clientIp.startsWith(range.split('/')[0].substring(0, range.indexOf('.')))
      })

      checks.vpnAccess = {
        status: isVpnAccess ? 'ok' : 'warning',
        message: isVpnAccess ? undefined : `Access from non-VPN IP: ${clientIp}`
      }
    }

    // Overall health status
    const hasErrors = Object.values(checks).some(check => check.status === 'error')
    const overallStatus = hasErrors ? 'unhealthy' : 'healthy'

    const response = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      server: 'staff',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      checks
    }

    return NextResponse.json(response, {
      status: hasErrors ? 503 : 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  } catch (error) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        server: 'staff',
        error: error instanceof Error ? error.message : 'Unknown error',
        checks
      },
      { status: 503 }
    )
  }
}
