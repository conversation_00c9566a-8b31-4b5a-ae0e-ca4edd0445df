import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const createAnnouncementSchema = z.object({
  title: z.string().min(1, "Title is required"),
  content: z.string().min(1, "Content is required"),
  priority: z.enum(["LOW", "MEDIUM", "HIGH"]).default("MEDIUM"),
  targetAudience: z.enum(["ALL", "STUDENTS", "TEACHERS", "ACADEMIC_MANAGERS"]).default("ALL"),
  isActive: z.boolean().default(true),
})

const updateAnnouncementSchema = z.object({
  title: z.string().min(1, "Title is required").optional(),
  content: z.string().min(1, "Content is required").optional(),
  priority: z.enum(["LOW", "MEDIUM", "HIGH"]).optional(),
  targetAudience: z.enum(["ALL", "STUDENTS", "TEACHERS", "ACADEMIC_MANAGERS"]).optional(),
  isActive: z.boolean().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const priority = searchParams.get("priority")
    const targetAudience = searchParams.get("targetAudience")
    const isActive = searchParams.get("isActive")
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const skip = (page - 1) * limit

    let where: any = {}

    if (priority) {
      where.priority = priority
    }

    if (targetAudience) {
      where.targetAudience = targetAudience
    }

    if (isActive !== null) {
      where.isActive = isActive === "true"
    }

    const [announcements, total] = await Promise.all([
      prisma.announcement.findMany({
        where,
        skip,
        take: limit,
        include: {
          author: {
            select: { id: true, name: true, role: true }
          }
        },
        orderBy: { createdAt: "desc" }
      }),
      prisma.announcement.count({ where })
    ])

    return NextResponse.json({
      announcements,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error("Error fetching announcements:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createAnnouncementSchema.parse(body)

    // Create announcement
    const announcement = await prisma.announcement.create({
      data: {
        ...validatedData,
        authorId: session.user.id,
      },
      include: {
        author: {
          select: { id: true, name: true, role: true }
        }
      }
    })

    // If announcement targets students, sync to students server
    if (validatedData.targetAudience === "STUDENTS" || validatedData.targetAudience === "ALL") {
      try {
        // TODO: Implement inter-server announcement sync
        console.log("Syncing announcement to students server:", announcement.id)
      } catch (error) {
        console.error("Error syncing announcement to students:", error)
      }
    }

    return NextResponse.json(announcement, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error creating announcement:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
