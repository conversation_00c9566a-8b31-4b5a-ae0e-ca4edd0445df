import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const submitResultsSchema = z.object({
  score: z.number().min(0, "Score must be non-negative"),
  passed: z.boolean().optional(),
  results: z.record(z.any()).optional(), // Flexible results object
  answers: z.array(z.object({
    questionId: z.string(),
    answer: z.string(),
    isCorrect: z.boolean().optional(),
    points: z.number().optional()
  })).optional(),
  completedAt: z.string().optional().transform((str) => str ? new Date(str) : new Date()),
  notes: z.string().optional(),
})

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "TEACHER", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = submitResultsSchema.parse(body)

    // Calculate pass/fail if not provided
    if (validatedData.passed === undefined) {
      // Assume 60% is passing grade
      const passingScore = 60
      const percentage = (validatedData.score / 100) * 100 // Assuming maxScore is 100
      validatedData.passed = percentage >= passingScore
    }

    // TODO: Submit results via inter-server API call to students server
    // For now, we'll simulate the response
    const updatedAssessment = {
      id: params.id,
      score: validatedData.score,
      passed: validatedData.passed,
      results: validatedData.results,
      answers: validatedData.answers,
      completedAt: validatedData.completedAt,
      notes: validatedData.notes,
      gradedBy: session.user.id,
      gradedAt: new Date(),
      updatedAt: new Date()
    }

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: session.user.id,
        action: "ASSESSMENT_GRADED",
        details: {
          assessmentId: params.id,
          score: validatedData.score,
          passed: validatedData.passed
        },
        ipAddress: request.headers.get("x-forwarded-for") || "unknown"
      }
    })

    return NextResponse.json(updatedAssessment)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error submitting assessment results:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // TODO: Fetch assessment results from students server via inter-server API
    // For now, we'll return mock results
    const results = {
      assessmentId: params.id,
      score: 85,
      maxScore: 100,
      passed: true,
      percentage: 85,
      results: {
        listening: 20,
        reading: 22,
        writing: 21,
        speaking: 22
      },
      answers: [
        {
          questionId: "q1",
          answer: "Option A",
          isCorrect: true,
          points: 2
        }
      ],
      completedAt: new Date(),
      gradedBy: "teacher_123",
      gradedAt: new Date()
    }

    return NextResponse.json(results)
  } catch (error) {
    console.error("Error fetching assessment results:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
