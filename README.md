# Inno CRM System

A comprehensive Customer Relationship Management system designed specifically for language learning centers, built with Next.js and featuring separate staff and student portals.

## 🏗️ Architecture

This CRM system consists of two main applications:

- **Staff Portal** (`inno-crm-staff/`) - Administrative interface for managing students, groups, courses, and operations
- **Student Portal** (`inno-crm-students/`) - Student-facing interface for accessing courses, assessments, and personal information

## ✨ Features

### Staff Portal Features
- **Student Management** - Complete student lifecycle management with enrollment, progress tracking, and status updates
- **Group & Course Management** - Create and manage learning groups, assign teachers, and track schedules
- **Lead Management** - Track potential students from inquiry to enrollment
- **Payment Management** - Handle payments, track debts, and generate financial reports
- **Attendance Tracking** - Record and monitor student attendance across all sessions
- **Assessment System** - Create, assign, and grade various types of assessments and tests
- **Messaging System** - Send announcements and messages to students and staff
- **Advanced Search & Filtering** - Global search across all entities with advanced filtering options
- **Data Export** - Export data in multiple formats (CSV, JSON, Excel)
- **Role-based Access Control** - Different permission levels for admins, managers, teachers, and academic managers

### Student Portal Features
- **Personal Dashboard** - Overview of courses, upcoming sessions, and important announcements
- **Course Access** - View enrolled courses, materials, and progress
- **Assessment Taking** - Complete assigned tests and view results
- **Schedule Management** - View class schedules and upcoming sessions
- **Payment Tracking** - View payment history and outstanding balances
- **Message Center** - Receive announcements and messages from staff
- **Profile Management** - Update personal information and preferences

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- PostgreSQL 14+
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd crm-staff
   ```

2. **Install dependencies for both projects**
   ```bash
   # Staff portal
   cd inno-crm-staff
   npm install
   
   # Student portal
   cd ../inno-crm-students
   npm install
   ```

3. **Set up environment variables**
   
   Create `.env.local` files in both project directories:
   
   **inno-crm-staff/.env.local**
   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/crm_staff"
   NEXTAUTH_SECRET="your-secret-key"
   NEXTAUTH_URL="http://localhost:3000"
   STUDENTS_SERVER_URL="http://localhost:3001"
   INTER_SERVER_SECRET="your-inter-server-secret"
   ```
   
   **inno-crm-students/.env.local**
   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/crm_students"
   NEXTAUTH_SECRET="your-secret-key"
   NEXTAUTH_URL="http://localhost:3001"
   STAFF_SERVER_URL="http://localhost:3000"
   INTER_SERVER_SECRET="your-inter-server-secret"
   ```

4. **Set up databases**
   ```bash
   # Staff database
   cd inno-crm-staff
   npx prisma generate
   npx prisma db push
   npx prisma db seed
   
   # Student database
   cd ../inno-crm-students
   npx prisma generate
   npx prisma db push
   npx prisma db seed
   ```

5. **Start development servers**
   ```bash
   # Terminal 1 - Staff portal
   cd inno-crm-staff
   npm run dev
   
   # Terminal 2 - Student portal
   cd inno-crm-students
   npm run dev
   ```

6. **Access the applications**
   - Staff Portal: http://localhost:3000
   - Student Portal: http://localhost:3001

## 🧪 Testing

### Running Tests

```bash
# Staff portal tests
cd inno-crm-staff
npm test

# Student portal tests
cd inno-crm-students
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

### Test Structure

```
__tests__/
├── api/                 # API route tests
├── components/          # Component tests
├── hooks/              # Custom hook tests
├── utils/              # Utility function tests
└── integration/        # Integration tests
```

## 📚 Documentation

- [API Documentation](./docs/api.md)
- [Database Schema](./docs/database.md)
- [Deployment Guide](./docs/deployment.md)
- [Contributing Guidelines](./docs/contributing.md)
- [Security Guidelines](./docs/security.md)

## 🛠️ Technology Stack

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS, shadcn/ui components
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js
- **Testing**: Jest, React Testing Library
- **State Management**: React hooks and context
- **Form Handling**: React Hook Form with Zod validation

## 📁 Project Structure

```
crm-staff/
├── inno-crm-staff/          # Staff portal application
│   ├── src/
│   │   ├── app/             # Next.js app directory
│   │   ├── components/      # Reusable UI components
│   │   ├── lib/            # Utility libraries and configurations
│   │   └── hooks/          # Custom React hooks
│   ├── prisma/             # Database schema and migrations
│   ├── __tests__/          # Test files
│   └── public/             # Static assets
├── inno-crm-students/       # Student portal application
│   └── [similar structure]
├── docs/                   # Documentation
└── plans/                  # Project planning documents
```

## 🔐 Security Features

- Role-based access control (RBAC)
- Session-based authentication
- Input validation and sanitization
- SQL injection prevention via Prisma
- XSS protection
- CSRF protection
- Rate limiting on API endpoints
- Secure inter-server communication

## 🚀 Deployment

See [Deployment Guide](./docs/deployment.md) for detailed instructions on deploying to production environments.

## 🤝 Contributing

Please read our [Contributing Guidelines](./docs/contributing.md) before submitting pull requests.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation in the `docs/` folder
- Review the planning documents in the `plans/` folder

## 🔄 Version History

- **v1.0.0** - Initial release with core CRM functionality
- **v1.1.0** - Added assessment system and messaging features
- **v1.2.0** - Enhanced search and filtering capabilities
- **v1.3.0** - Improved testing coverage and documentation
