import { NextRequest, NextResponse } from 'next/server'
import { createInterServerAuthMiddleware, createRateLimitMiddleware, combineMiddleware } from '@/lib/middleware/inter-server-auth'
import { getStudentsClient } from '@/lib/api-clients/students-client'

// Configure middleware
const authMiddleware = createInterServerAuthMiddleware({
  validApiKeys: [process.env.STUDENTS_API_KEY || ''],
  secretKey: process.env.STUDENTS_SECRET_KEY || '',
})

const rateLimitMiddleware = createRateLimitMiddleware({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 100, // 100 requests per minute
})

const middleware = combineMiddleware(authMiddleware, rateLimitMiddleware)

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Apply middleware
  const middlewareResponse = await middleware(request)
  if (middlewareResponse) {
    return middlewareResponse
  }

  try {
    const { id } = params

    // Get student from students server
    const studentsClient = getStudentsClient()
    const result = await studentsClient.getStudent(id)

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: result.error?.includes('not found') ? 404 : 500 }
      )
    }

    return NextResponse.json(result.data)
  } catch (error) {
    console.error('Error fetching student:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Apply middleware
  const middlewareResponse = await middleware(request)
  if (middlewareResponse) {
    return middlewareResponse
  }

  try {
    const { id } = params
    const data = await request.json()

    // Update student in students server
    const studentsClient = getStudentsClient()
    const result = await studentsClient.updateStudent(id, data)

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: result.error?.includes('not found') ? 404 : 500 }
      )
    }

    // TODO: Update student reference in staff database
    
    return NextResponse.json(result.data)
  } catch (error) {
    console.error('Error updating student:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Apply middleware
  const middlewareResponse = await middleware(request)
  if (middlewareResponse) {
    return middlewareResponse
  }

  try {
    const { id } = params

    // Delete student from students server
    const studentsClient = getStudentsClient()
    const result = await studentsClient.deleteStudent(id)

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: result.error?.includes('not found') ? 404 : 500 }
      )
    }

    // TODO: Delete student reference from staff database
    
    return NextResponse.json({ message: 'Student deleted successfully' })
  } catch (error) {
    console.error('Error deleting student:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
