import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const exportSchema = z.object({
  type: z.enum(["students", "staff", "leads", "groups", "courses", "payments", "attendance", "assessments"]),
  format: z.enum(["csv", "xlsx", "json"]).default("csv"),
  filters: z.record(z.any()).optional(),
  fields: z.array(z.string()).optional(),
  dateRange: z.object({
    from: z.string().optional(),
    to: z.string().optional()
  }).optional()
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = exportSchema.parse(body)

    let data: any[] = []
    let filename = ""

    switch (validatedData.type) {
      case "students":
        data = await exportStudents(validatedData.filters, validatedData.fields)
        filename = `students_export_${new Date().toISOString().split('T')[0]}`
        break
      
      case "staff":
        data = await exportStaff(validatedData.filters, validatedData.fields)
        filename = `staff_export_${new Date().toISOString().split('T')[0]}`
        break
      
      case "leads":
        data = await exportLeads(validatedData.filters, validatedData.fields)
        filename = `leads_export_${new Date().toISOString().split('T')[0]}`
        break
      
      case "groups":
        data = await exportGroups(validatedData.filters, validatedData.fields)
        filename = `groups_export_${new Date().toISOString().split('T')[0]}`
        break
      
      case "courses":
        data = await exportCourses(validatedData.filters, validatedData.fields)
        filename = `courses_export_${new Date().toISOString().split('T')[0]}`
        break
      
      case "payments":
        data = await exportPayments(validatedData.filters, validatedData.fields, validatedData.dateRange)
        filename = `payments_export_${new Date().toISOString().split('T')[0]}`
        break
      
      case "attendance":
        data = await exportAttendance(validatedData.filters, validatedData.fields, validatedData.dateRange)
        filename = `attendance_export_${new Date().toISOString().split('T')[0]}`
        break
      
      case "assessments":
        // This would need to fetch from students server
        data = []
        filename = `assessments_export_${new Date().toISOString().split('T')[0]}`
        break
      
      default:
        return NextResponse.json({ error: "Invalid export type" }, { status: 400 })
    }

    if (validatedData.format === "csv") {
      const csv = convertToCSV(data)
      
      return new NextResponse(csv, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="${filename}.csv"`
        }
      })
    } else if (validatedData.format === "json") {
      return new NextResponse(JSON.stringify(data, null, 2), {
        headers: {
          'Content-Type': 'application/json',
          'Content-Disposition': `attachment; filename="${filename}.json"`
        }
      })
    } else {
      // For XLSX, we'd need a library like xlsx
      return NextResponse.json({ error: "XLSX format not yet implemented" }, { status: 501 })
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error exporting data:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

async function exportStudents(filters?: Record<string, any>, fields?: string[]) {
  const where = buildWhereClause(filters)
  
  const students = await prisma.studentReference.findMany({
    where,
    include: {
      currentGroup: {
        include: {
          course: true,
          teacher: {
            include: {
              user: true
            }
          }
        }
      },
      paymentOverview: {
        orderBy: { createdAt: "desc" },
        take: 1
      }
    },
    orderBy: { createdAt: "desc" }
  })

  return students.map(student => ({
    id: student.id,
    name: student.name,
    phone: student.phone,
    status: student.status,
    level: student.level,
    branch: student.branch,
    group: student.currentGroup?.name || "No group",
    course: student.currentGroup?.course.name || "No course",
    teacher: student.currentGroup?.teacher.user.name || "No teacher",
    totalPaid: student.paymentOverview[0]?.totalPaid || 0,
    totalDebt: student.paymentOverview[0]?.totalDebt || 0,
    createdAt: student.createdAt.toISOString(),
    updatedAt: student.updatedAt.toISOString()
  }))
}

async function exportStaff(filters?: Record<string, any>, fields?: string[]) {
  const where = buildWhereClause(filters)
  
  const staff = await prisma.user.findMany({
    where,
    include: {
      teacherProfile: true
    },
    orderBy: { createdAt: "desc" }
  })

  return staff.map(user => ({
    id: user.id,
    name: user.name,
    email: user.email,
    phone: user.phone,
    role: user.role,
    isTeacher: !!user.teacherProfile,
    createdAt: user.createdAt.toISOString(),
    updatedAt: user.updatedAt.toISOString()
  }))
}

async function exportLeads(filters?: Record<string, any>, fields?: string[]) {
  const where = buildWhereClause(filters)
  
  const leads = await prisma.lead.findMany({
    where,
    orderBy: { createdAt: "desc" }
  })

  return leads.map(lead => ({
    id: lead.id,
    name: lead.name,
    phone: lead.phone,
    status: lead.status,
    coursePreference: lead.coursePreference,
    source: lead.source,
    notes: lead.notes,
    createdAt: lead.createdAt.toISOString(),
    updatedAt: lead.updatedAt.toISOString()
  }))
}

async function exportGroups(filters?: Record<string, any>, fields?: string[]) {
  const where = buildWhereClause(filters)
  
  const groups = await prisma.group.findMany({
    where,
    include: {
      course: true,
      teacher: {
        include: {
          user: true
        }
      },
      _count: {
        select: {
          studentReferences: true
        }
      }
    },
    orderBy: { createdAt: "desc" }
  })

  return groups.map(group => ({
    id: group.id,
    name: group.name,
    course: group.course.name,
    teacher: group.teacher.user.name,
    room: group.room,
    schedule: group.schedule,
    studentCount: group._count.studentReferences,
    isActive: group.isActive,
    createdAt: group.createdAt.toISOString(),
    updatedAt: group.updatedAt.toISOString()
  }))
}

async function exportCourses(filters?: Record<string, any>, fields?: string[]) {
  const where = buildWhereClause(filters)
  
  const courses = await prisma.course.findMany({
    where,
    include: {
      _count: {
        select: {
          groups: true
        }
      }
    },
    orderBy: { createdAt: "desc" }
  })

  return courses.map(course => ({
    id: course.id,
    name: course.name,
    description: course.description,
    level: course.level,
    duration: course.duration,
    price: course.price,
    groupCount: course._count.groups,
    isActive: course.isActive,
    createdAt: course.createdAt.toISOString(),
    updatedAt: course.updatedAt.toISOString()
  }))
}

async function exportPayments(filters?: Record<string, any>, fields?: string[], dateRange?: { from?: string, to?: string }) {
  let where = buildWhereClause(filters)
  
  if (dateRange?.from || dateRange?.to) {
    where = {
      ...where,
      createdAt: {
        ...(dateRange.from && { gte: new Date(dateRange.from) }),
        ...(dateRange.to && { lte: new Date(dateRange.to) })
      }
    }
  }
  
  const payments = await prisma.payment.findMany({
    where,
    include: {
      studentReference: true
    },
    orderBy: { createdAt: "desc" }
  })

  return payments.map(payment => ({
    id: payment.id,
    student: payment.studentReference.name,
    studentPhone: payment.studentReference.phone,
    amount: payment.amount,
    method: payment.method,
    status: payment.status,
    description: payment.description,
    createdAt: payment.createdAt.toISOString()
  }))
}

async function exportAttendance(filters?: Record<string, any>, fields?: string[], dateRange?: { from?: string, to?: string }) {
  let where = buildWhereClause(filters)
  
  if (dateRange?.from || dateRange?.to) {
    where = {
      ...where,
      date: {
        ...(dateRange.from && { gte: new Date(dateRange.from) }),
        ...(dateRange.to && { lte: new Date(dateRange.to) })
      }
    }
  }
  
  const attendance = await prisma.attendance.findMany({
    where,
    include: {
      studentReference: true,
      group: {
        include: {
          course: true
        }
      }
    },
    orderBy: { date: "desc" }
  })

  return attendance.map(record => ({
    id: record.id,
    student: record.studentReference.name,
    studentPhone: record.studentReference.phone,
    group: record.group.name,
    course: record.group.course.name,
    date: record.date.toISOString().split('T')[0],
    status: record.status,
    notes: record.notes,
    createdAt: record.createdAt.toISOString()
  }))
}

function buildWhereClause(filters?: Record<string, any>) {
  if (!filters) return {}
  
  const where: any = {}
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      where[key] = value
    }
  })
  
  return where
}

function convertToCSV(data: any[]): string {
  if (data.length === 0) return ""
  
  const headers = Object.keys(data[0])
  const csvHeaders = headers.join(",")
  
  const csvRows = data.map(row => 
    headers.map(header => {
      const value = row[header]
      // Escape commas and quotes in CSV
      if (typeof value === "string" && (value.includes(",") || value.includes('"'))) {
        return `"${value.replace(/"/g, '""')}"`
      }
      return value
    }).join(",")
  )
  
  return [csvHeaders, ...csvRows].join("\n")
}
