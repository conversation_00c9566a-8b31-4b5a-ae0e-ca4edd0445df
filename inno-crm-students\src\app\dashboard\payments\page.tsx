"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { 
  DollarSign, 
  CreditCard, 
  AlertCircle, 
  CheckCircle, 
  Clock,
  Calendar,
  TrendingUp,
  Download
} from "lucide-react"
import DashboardLayout from "@/components/dashboard/layout"

interface Payment {
  id: string
  amount: number
  method: string
  status: string
  description?: string
  transactionId?: string
  dueDate?: string
  paidDate?: string
  createdAt: string
}

interface PaymentSummary {
  totalPaid: number
  statusBreakdown: Array<{
    status: string
    _sum: { amount: number }
    _count: { _all: number }
  }>
  upcomingPayments: Payment[]
}

const statusLabels = {
  PAID: "Paid",
  DEBT: "Outstanding",
  REFUNDED: "Refunded"
}

const statusColors = {
  PAID: "bg-green-100 text-green-800",
  DEBT: "bg-red-100 text-red-800",
  REFUNDED: "bg-blue-100 text-blue-800"
}

const methodLabels = {
  CASH: "Cash",
  CARD: "Card"
}

export default function StudentPaymentsPage() {
  const { data: session } = useSession()
  const [payments, setPayments] = useState<Payment[]>([])
  const [summary, setSummary] = useState<PaymentSummary | null>(null)
  const [loading, setLoading] = useState(true)
  const [statusFilter, setStatusFilter] = useState("")
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  })

  const fetchPayments = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(statusFilter && { status: statusFilter })
      })

      const response = await fetch(`/api/payments?${params}`)
      if (response.ok) {
        const data = await response.json()
        setPayments(data.payments)
        setPagination(data.pagination)
        setSummary(data.summary)
      }
    } catch (error) {
      console.error("Error fetching payments:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPayments()
  }, [pagination.page, statusFilter])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PAID':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'DEBT':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      case 'REFUNDED':
        return <CreditCard className="h-4 w-4 text-blue-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getTotalDebt = () => {
    if (!summary) return 0
    const debtStatus = summary.statusBreakdown.find(s => s.status === 'DEBT')
    return debtStatus?._sum.amount || 0
  }

  const getTotalPaid = () => {
    if (!summary) return 0
    const paidStatus = summary.statusBreakdown.find(s => s.status === 'PAID')
    return paidStatus?._sum.amount || 0
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-blue-900">My Payments</h1>
          <p className="text-blue-600">View your payment history and outstanding balances</p>
        </div>

        {/* Summary Cards */}
        {summary && (
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <Card className="border-blue-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-blue-700">Total Paid</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-900">${getTotalPaid().toFixed(2)}</div>
                <p className="text-xs text-blue-600">
                  All time payments
                </p>
              </CardContent>
            </Card>

            <Card className="border-blue-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-blue-700">Outstanding</CardTitle>
                <AlertCircle className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">${getTotalDebt().toFixed(2)}</div>
                <p className="text-xs text-blue-600">
                  Amount due
                </p>
              </CardContent>
            </Card>

            <Card className="border-blue-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-blue-700">This Month</CardTitle>
                <TrendingUp className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-900">$0.00</div>
                <p className="text-xs text-blue-600">
                  Current month
                </p>
              </CardContent>
            </Card>

            <Card className="border-blue-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-blue-700">Next Due</CardTitle>
                <Calendar className="h-4 w-4 text-orange-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {summary.upcomingPayments.length > 0 ? (
                    new Date(summary.upcomingPayments[0].dueDate!).toLocaleDateString()
                  ) : (
                    "None"
                  )}
                </div>
                <p className="text-xs text-blue-600">
                  {summary.upcomingPayments.length > 0 ? (
                    `$${summary.upcomingPayments[0].amount.toFixed(2)}`
                  ) : (
                    "No upcoming payments"
                  )}
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Upcoming Payments Alert */}
        {summary && summary.upcomingPayments.length > 0 && (
          <Card className="border-orange-200 bg-orange-50">
            <CardHeader>
              <CardTitle className="text-orange-800 flex items-center">
                <AlertCircle className="h-5 w-5 mr-2" />
                Upcoming Payments
              </CardTitle>
              <CardDescription className="text-orange-700">
                You have {summary.upcomingPayments.length} payment(s) due in the next 30 days
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {summary.upcomingPayments.map((payment) => (
                  <div key={payment.id} className="flex justify-between items-center p-3 bg-white rounded-lg border border-orange-200">
                    <div>
                      <div className="font-medium text-orange-900">{payment.description || "Payment"}</div>
                      <div className="text-sm text-orange-700">
                        Due: {new Date(payment.dueDate!).toLocaleDateString()}
                      </div>
                    </div>
                    <div className="text-lg font-bold text-orange-800">
                      ${payment.amount.toFixed(2)}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Payment History */}
        <Card className="border-blue-200">
          <CardHeader>
            <CardTitle className="text-blue-900">Payment History</CardTitle>
            <CardDescription>
              View all your payment transactions and receipts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center mb-6">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-48 border-blue-300">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Payments</SelectItem>
                  {Object.entries(statusLabels).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Button variant="outline" size="sm" className="border-blue-300 text-blue-700 hover:bg-blue-50">
                <Download className="h-4 w-4 mr-2" />
                Download Receipt
              </Button>
            </div>

            <div className="border border-blue-200 rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow className="border-blue-200">
                    <TableHead className="text-blue-700">Date</TableHead>
                    <TableHead className="text-blue-700">Description</TableHead>
                    <TableHead className="text-blue-700">Amount</TableHead>
                    <TableHead className="text-blue-700">Method</TableHead>
                    <TableHead className="text-blue-700">Status</TableHead>
                    <TableHead className="text-blue-700">Transaction ID</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        Loading...
                      </TableCell>
                    </TableRow>
                  ) : payments.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        No payments found
                      </TableCell>
                    </TableRow>
                  ) : (
                    payments.map((payment) => (
                      <TableRow key={payment.id} className="border-blue-100">
                        <TableCell>
                          <div className="text-sm">
                            {payment.paidDate ? new Date(payment.paidDate).toLocaleDateString() : new Date(payment.createdAt).toLocaleDateString()}
                          </div>
                          {payment.dueDate && payment.status === 'DEBT' && (
                            <div className="text-xs text-red-600">
                              Due: {new Date(payment.dueDate).toLocaleDateString()}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="font-medium text-blue-900">
                            {payment.description || "Payment"}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium text-blue-900">
                            ${payment.amount.toFixed(2)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {methodLabels[payment.method as keyof typeof methodLabels]}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            {getStatusIcon(payment.status)}
                            <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[payment.status as keyof typeof statusColors]}`}>
                              {statusLabels[payment.status as keyof typeof statusLabels]}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm text-gray-500">
                            {payment.transactionId || "-"}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            {pagination.pages > 1 && (
              <div className="flex items-center justify-between mt-4">
                <p className="text-sm text-blue-700">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to{" "}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
                  {pagination.total} results
                </p>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                    disabled={pagination.page === 1}
                    className="border-blue-300 text-blue-700 hover:bg-blue-50"
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                    disabled={pagination.page === pagination.pages}
                    className="border-blue-300 text-blue-700 hover:bg-blue-50"
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
