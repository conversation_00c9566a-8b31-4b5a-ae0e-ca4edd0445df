import { createMocks } from 'node-mocks-http'
import handler from '@/app/api/students/route'
import { prisma } from '@/lib/database/prisma'
import { getServerSession } from 'next-auth'

// Mock the dependencies
jest.mock('next-auth')
jest.mock('@/lib/database/prisma')

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>
const mockPrisma = prisma as jest.Mocked<typeof prisma>

describe('/api/students', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET', () => {
    it('should return students list for authenticated admin user', async () => {
      // Mock session
      mockGetServerSession.mockResolvedValue({
        user: {
          id: 'admin-id',
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'ADMIN',
        },
        expires: '2024-12-31',
      })

      // Mock database response
      const mockStudents = [
        {
          id: 'student-1',
          name: '<PERSON>',
          phone: '1234567890',
          status: 'ACTIVE',
          level: 'A1',
          branch: 'main',
          createdAt: new Date(),
          updatedAt: new Date(),
          currentGroup: {
            id: 'group-1',
            name: 'Group A1-1',
            course: {
              id: 'course-1',
              name: 'English A1',
            },
          },
        },
      ]

      mockPrisma.studentReference.findMany.mockResolvedValue(mockStudents as any)
      mockPrisma.studentReference.count.mockResolvedValue(1)

      const { req } = createMocks({
        method: 'GET',
        url: '/api/students',
      })

      const response = await handler.GET(req as any)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.students).toHaveLength(1)
      expect(data.students[0].name).toBe('John Doe')
      expect(data.pagination.total).toBe(1)
    })

    it('should return 401 for unauthenticated user', async () => {
      mockGetServerSession.mockResolvedValue(null)

      const { req } = createMocks({
        method: 'GET',
        url: '/api/students',
      })

      const response = await handler.GET(req as any)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })

    it('should filter students by search term', async () => {
      mockGetServerSession.mockResolvedValue({
        user: {
          id: 'admin-id',
          role: 'ADMIN',
        },
        expires: '2024-12-31',
      } as any)

      const { req } = createMocks({
        method: 'GET',
        url: '/api/students?search=john',
      })

      mockPrisma.studentReference.findMany.mockResolvedValue([])
      mockPrisma.studentReference.count.mockResolvedValue(0)

      await handler.GET(req as any)

      expect(mockPrisma.studentReference.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              { name: { contains: 'john', mode: 'insensitive' } },
              { phone: { contains: 'john', mode: 'insensitive' } },
            ]),
          }),
        })
      )
    })
  })

  describe('POST', () => {
    it('should create a new student for authorized user', async () => {
      mockGetServerSession.mockResolvedValue({
        user: {
          id: 'admin-id',
          role: 'ADMIN',
        },
        expires: '2024-12-31',
      } as any)

      const newStudent = {
        name: 'Jane Doe',
        phone: '0987654321',
        status: 'ACTIVE',
        level: 'A2',
        branch: 'main',
      }

      const createdStudent = {
        id: 'student-2',
        ...newStudent,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockPrisma.studentReference.create.mockResolvedValue(createdStudent as any)

      const { req } = createMocks({
        method: 'POST',
        url: '/api/students',
        body: newStudent,
      })

      const response = await handler.POST(req as any)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.name).toBe('Jane Doe')
      expect(mockPrisma.studentReference.create).toHaveBeenCalledWith({
        data: expect.objectContaining(newStudent),
      })
    })

    it('should return 401 for unauthorized user', async () => {
      mockGetServerSession.mockResolvedValue({
        user: {
          id: 'user-id',
          role: 'STUDENT',
        },
        expires: '2024-12-31',
      } as any)

      const { req } = createMocks({
        method: 'POST',
        url: '/api/students',
        body: {
          name: 'Jane Doe',
          phone: '0987654321',
        },
      })

      const response = await handler.POST(req as any)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })

    it('should return 400 for invalid data', async () => {
      mockGetServerSession.mockResolvedValue({
        user: {
          id: 'admin-id',
          role: 'ADMIN',
        },
        expires: '2024-12-31',
      } as any)

      const { req } = createMocks({
        method: 'POST',
        url: '/api/students',
        body: {
          // Missing required fields
          name: '',
        },
      })

      const response = await handler.POST(req as any)

      expect(response.status).toBe(400)
    })
  })
})
